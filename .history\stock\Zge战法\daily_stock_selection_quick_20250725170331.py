#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Z哥战法每日选股脚本 - 快速版
仅运行选股，不更新数据，避免网络和编码问题
"""

import os
import sys
import datetime
import logging
from pathlib import Path
import json
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('daily_selection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def is_trading_day():
    """
    简单判断是否为交易日（周一到周五）
    """
    today = datetime.date.today()
    weekday = today.weekday()  # 0=Monday, 6=Sunday
    return weekday < 5  # Monday to Friday

def run_stock_selection():
    """
    运行选股程序
    """
    logger.info("开始执行选股...")
    try:
        # 直接导入并运行select_stock模块
        import select_stock
        
        # 保存原始argv
        original_argv = sys.argv.copy()
        
        # 模拟命令行参数
        sys.argv = [
            'select_stock.py',
            '--data-dir', './data',
            '--config', './configs.json'
        ]
        
        # 运行主函数
        select_stock.main()
        
        # 恢复原始argv
        sys.argv = original_argv
        
        logger.info("✅ 选股程序执行成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 选股程序执行失败: {e}")
        return False

def parse_selection_results_from_log():
    """
    从日志文件解析选股结果
    """
    results = {}
    log_file = Path("select_results.log")
    
    if not log_file.exists():
        logger.warning("选股结果日志文件不存在")
        return results
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到最后一次运行的结果
        lines = content.split('\n')
        last_run_start = -1
        for i, line in enumerate(lines):
            if '未指定 --date，使用最近日期' in line:
                last_run_start = i
        
        if last_run_start >= 0:
            last_run_content = '\n'.join(lines[last_run_start:])
            results = parse_selection_results(last_run_content)
            
    except Exception as e:
        logger.error(f"解析选股结果失败: {e}")
    
    return results

def parse_selection_results(output):
    """
    解析选股结果
    """
    results = {}
    current_strategy = None
    
    if output is None:
        output = ""
    
    lines = output.split('\n')
    for line in lines:
        line = line.strip()
        
        # 跳过空行和处理INFO行
        if not line or '[INFO]' in line[:30]:
            if '[INFO]' in line:
                info_pos = line.find('[INFO]')
                if info_pos >= 0:
                    line = line[info_pos + 6:].strip()
        
        if not line:
            continue
            
        # 匹配策略名称
        strategy_match = re.search(r'============== 选股结果 \[(.+?)\] ==============', line)
        if strategy_match:
            current_strategy = strategy_match.group(1)
            results[current_strategy] = {'date': '', 'count': 0, 'stocks': []}
            continue
            
        # 匹配交易日
        date_match = re.search(r'交易日: (\d{4}-\d{2}-\d{2})', line)
        if date_match and current_strategy:
            results[current_strategy]['date'] = date_match.group(1)
            continue
            
        # 匹配股票数量
        count_match = re.search(r'符合条件股票数: (\d+)', line)
        if count_match and current_strategy:
            results[current_strategy]['count'] = int(count_match.group(1))
            continue
            
        # 匹配股票代码
        if current_strategy and ',' in line and re.match(r'^[0-9,\s]+$', line):
            stocks = [stock.strip() for stock in line.split(',') if stock.strip()]
            results[current_strategy]['stocks'] = stocks
            continue
            
        # 匹配单个股票代码
        if current_strategy and re.match(r'^\d{6}$', line.strip()):
            results[current_strategy]['stocks'] = [line.strip()]
            continue
            
        # 匹配无符合条件股票
        if '无符合条件股票' in line and current_strategy:
            results[current_strategy]['stocks'] = []
            continue
    
    return results

def update_markdown_report(results):
    """
    更新markdown报告
    """
    markdown_file = Path('daily_selection_report.md')
    today = datetime.date.today().strftime('%Y-%m-%d')
    
    # 准备新的报告内容
    new_content = f"\n## {today} 选股结果\n\n"
    new_content += f"**更新时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    total_stocks = 0
    for strategy, data in results.items():
        count = data['count']
        stocks = data['stocks']
        total_stocks += count
        
        new_content += f"### {strategy}\n"
        new_content += f"- **符合条件股票数**: {count}只\n"
        
        if stocks:
            # 每行显示10只股票
            stock_lines = []
            for i in range(0, len(stocks), 10):
                stock_line = ', '.join(stocks[i:i+10])
                stock_lines.append(stock_line)
            new_content += f"- **股票代码**: \n"
            for line in stock_lines:
                new_content += f"  - {line}\n"
        else:
            new_content += f"- **股票代码**: 无符合条件股票\n"
        new_content += "\n"
    
    new_content += f"**今日总计**: {total_stocks}只股票符合条件\n\n"
    new_content += "---\n"
    
    # 读取现有内容或创建新文件
    if markdown_file.exists():
        with open(markdown_file, 'r', encoding='utf-8') as f:
            existing_content = f.read()
    else:
        existing_content = "# Z哥战法每日选股报告\n\n"
        existing_content += "本报告记录每个交易日的选股结果，按时间倒序排列。\n\n"
        existing_content += "## 策略说明\n\n"
        existing_content += "- **少妇战法**: BBI上升 + KDJ低位 + MACD金叉\n"
        existing_content += "- **补票战法**: BBI上升 + 短长期RSV条件 + MACD金叉\n"
        existing_content += "- **TePu战法**: 放量突破 + KDJ低位 + MACD金叉\n"
        existing_content += "- **填坑战法**: 峰值回调 + KDJ低位\n\n"
        existing_content += "---\n\n"
    
    # 将新内容插入到现有内容的开头
    lines = existing_content.split('\n')
    insert_index = -1
    for i, line in enumerate(lines):
        if line.strip() == '---' and i > 5:
            insert_index = i + 1
            break
    
    if insert_index > 0:
        lines.insert(insert_index, new_content)
        final_content = '\n'.join(lines)
    else:
        final_content = existing_content + new_content
    
    # 写入文件
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    logger.info(f"✅ 报告已更新到 {markdown_file}")

def main():
    """
    主函数
    """
    logger.info("=" * 60)
    logger.info("Z哥战法每日选股 (快速版 - 仅选股)")
    logger.info(f"当前时间: {datetime.datetime.now()}")
    
    # 检查是否为交易日
    if not is_trading_day():
        logger.info("今天不是交易日，跳过执行")
        return
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    logger.info(f"工作目录: {script_dir}")
    
    try:
        # 步骤1: 运行选股（跳过数据更新）
        logger.info("步骤1: 执行选股程序（使用现有数据）")
        success = run_stock_selection()
        if not success:
            logger.error("选股程序执行失败，终止执行")
            return
        
        # 步骤2: 解析结果
        logger.info("步骤2: 解析选股结果")
        results = parse_selection_results_from_log()
        
        if not results:
            logger.warning("未能解析到选股结果，但任务已完成")
            logger.info("请检查 select_results.log 文件获取详细结果")
            return
        
        # 步骤3: 更新报告
        logger.info("步骤3: 更新markdown报告")
        update_markdown_report(results)
        
        # 输出详细结果
        logger.info("选股结果详情:")
        total = 0
        for strategy, data in results.items():
            count = data['count']
            stocks = data['stocks']
            total += count
            logger.info(f"  {strategy}: {count}只")
            if stocks:
                logger.info(f"    股票代码: {', '.join(stocks)}")
            else:
                logger.info(f"    股票代码: 无符合条件股票")
        logger.info(f"  总计: {total}只股票符合条件")
        
        logger.info("✅ 每日选股任务完成")
        
    except Exception as e:
        logger.error(f"❌ 执行过程中发生异常: {e}")
        raise
    
    finally:
        logger.info("Z哥战法每日选股结束")
        logger.info("=" * 60)

if __name__ == "__main__":
    main()
