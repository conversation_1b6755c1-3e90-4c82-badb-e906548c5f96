#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易日历功能
"""

import sys
import os
from datetime import datetime, date, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入函数
try:
    from zhangtingshuchu_dates import (
        get_trade_dates, 
        is_trading_day, 
        get_previous_trading_day
    )
except ImportError:
    # 如果文件名不同，尝试其他可能的名称
    try:
        from zhangtingshuchu-dates import (
            get_trade_dates, 
            is_trading_day, 
            get_previous_trading_day
        )
    except ImportError:
        print("无法导入交易日历函数，请检查文件名")
        sys.exit(1)

def test_trading_calendar():
    """测试交易日历功能"""
    print("=" * 60)
    print("测试交易日历功能")
    print("=" * 60)
    
    # 获取交易日历
    print("1. 获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    
    if trade_dates_df.empty:
        print("❌ 无法获取交易日历数据")
        return False
    
    trading_dates = sorted(trade_dates_df['trade_date'].dt.date.unique())
    print(f"✅ 成功获取交易日历，共 {len(trading_dates)} 个交易日")
    print(f"   最早交易日: {min(trading_dates)}")
    print(f"   最新交易日: {max(trading_dates)}")
    
    return trade_dates_df, trading_dates

def test_trading_day_validation(trade_dates_df, trading_dates):
    """测试交易日验证功能"""
    print("\n2. 测试交易日验证功能...")
    
    # 测试已知的交易日
    latest_trading_day = max(trading_dates)
    is_trading = is_trading_day(latest_trading_day, trade_dates_df)
    print(f"   {latest_trading_day} 是交易日: {is_trading} ✅" if is_trading else f"   {latest_trading_day} 是交易日: {is_trading} ❌")
    
    # 测试周末（通常不是交易日）
    # 找一个周六
    test_date = latest_trading_day
    while test_date.weekday() != 5:  # 5 = 周六
        test_date += timedelta(days=1)
    
    is_weekend_trading = is_trading_day(test_date, trade_dates_df)
    print(f"   {test_date} (周六) 是交易日: {is_weekend_trading} {'❌' if is_weekend_trading else '✅'}")
    
    # 测试节假日（选择一个明显的节假日，如元旦）
    new_year = date(latest_trading_day.year, 1, 1)
    is_holiday_trading = is_trading_day(new_year, trade_dates_df)
    print(f"   {new_year} (元旦) 是交易日: {is_holiday_trading} {'❌' if is_holiday_trading else '✅'}")

def test_previous_trading_day(trade_dates_df, trading_dates):
    """测试前一交易日获取功能"""
    print("\n3. 测试前一交易日获取功能...")
    
    # 测试最新交易日的前一交易日
    latest_trading_day = max(trading_dates)
    previous_day = get_previous_trading_day(latest_trading_day, trade_dates_df)
    
    if previous_day:
        print(f"   {latest_trading_day} 的前一交易日: {previous_day} ✅")
        
        # 验证前一交易日确实是交易日
        is_prev_trading = is_trading_day(previous_day, trade_dates_df)
        print(f"   验证 {previous_day} 是交易日: {is_prev_trading} ✅" if is_prev_trading else f"   验证 {previous_day} 是交易日: {is_prev_trading} ❌")
        
        # 验证日期顺序
        is_before = previous_day < latest_trading_day
        print(f"   验证日期顺序 ({previous_day} < {latest_trading_day}): {is_before} ✅" if is_before else f"   验证日期顺序: {is_before} ❌")
    else:
        print(f"   {latest_trading_day} 的前一交易日: None ❌")

def test_consecutive_trading_days(trade_dates_df, trading_dates):
    """测试连续交易日"""
    print("\n4. 测试连续交易日...")
    
    # 取最近的5个交易日进行测试
    recent_days = trading_dates[-5:]
    print(f"   最近5个交易日: {recent_days}")
    
    for i in range(1, len(recent_days)):
        current_day = recent_days[i]
        expected_previous = recent_days[i-1]
        actual_previous = get_previous_trading_day(current_day, trade_dates_df)
        
        if actual_previous == expected_previous:
            print(f"   {current_day} 的前一交易日: {actual_previous} ✅")
        else:
            print(f"   {current_day} 的前一交易日: 期望 {expected_previous}, 实际 {actual_previous} ❌")

def test_edge_cases(trade_dates_df, trading_dates):
    """测试边界情况"""
    print("\n5. 测试边界情况...")
    
    # 测试最早交易日的前一交易日
    earliest_trading_day = min(trading_dates)
    previous_of_earliest = get_previous_trading_day(earliest_trading_day, trade_dates_df)
    
    if previous_of_earliest is None:
        print(f"   最早交易日 {earliest_trading_day} 的前一交易日: None ✅")
    else:
        print(f"   最早交易日 {earliest_trading_day} 的前一交易日: {previous_of_earliest} ❌")
    
    # 测试未来日期
    future_date = max(trading_dates) + timedelta(days=365)
    is_future_trading = is_trading_day(future_date, trade_dates_df)
    print(f"   未来日期 {future_date} 是交易日: {is_future_trading} {'❌' if is_future_trading else '✅'}")

def main():
    """主测试函数"""
    print("🚀 开始测试交易日历功能")
    
    # 测试交易日历获取
    result = test_trading_calendar()
    if not result:
        print("❌ 交易日历获取失败，退出测试")
        return
    
    trade_dates_df, trading_dates = result
    
    # 测试各项功能
    test_trading_day_validation(trade_dates_df, trading_dates)
    test_previous_trading_day(trade_dates_df, trading_dates)
    test_consecutive_trading_days(trade_dates_df, trading_dates)
    test_edge_cases(trade_dates_df, trading_dates)
    
    print("\n" + "=" * 60)
    print("🎉 交易日历功能测试完成！")
    print("=" * 60)
    
    # 显示一些统计信息
    print(f"\n📊 交易日历统计:")
    print(f"   总交易日数: {len(trading_dates)}")
    print(f"   日期范围: {min(trading_dates)} 至 {max(trading_dates)}")
    
    # 计算最近一年的交易日数
    one_year_ago = max(trading_dates) - timedelta(days=365)
    recent_trading_days = [d for d in trading_dates if d >= one_year_ago]
    print(f"   最近一年交易日数: {len(recent_trading_days)}")

if __name__ == "__main__":
    main()
