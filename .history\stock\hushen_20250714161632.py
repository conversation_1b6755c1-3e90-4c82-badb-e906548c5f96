import pywencai
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>


def get_market_data(date=None):
    """获取沪深股市场数据
    包括：成交量、涨停家数、跌停家数、上涨家数、下跌家数
    """
    try:
        # 如果没有指定日期，使用最近交易日
        if not date:
            date = datetime.now().strftime('%Y%m%d')
            
        # 构建问财查询语句
        query = f"{date}沪深股成交量，涨停家数，跌停家数，上涨家数，下跌家数"
        
        # 获取数据
        df = pywencai.get(query=query, loop=True)
        
        if isinstance(df, pd.DataFrame) and not df.empty:
            # 提取数据
            result = {
                '日期': date,
                '成交量(亿)': float(df.iloc[0]['成交量']) / 100000000 if '成交量' in df.columns else 0,
                '涨停家数': int(df.iloc[0]['涨停家数']) if '涨停家数' in df.columns else 0,
                '跌停家数': int(df.iloc[0]['跌停家数']) if '跌停家数' in df.columns else 0,
                '上涨家数': int(df.iloc[0]['上涨家数']) if '上涨家数' in df.columns else 0,
                '下跌家数': int(df.iloc[0]['下跌家数']) if '下跌家数' in df.columns else 0
            }
            
            print(f"\n{date}市场数据:")
            print(f"成交量: {result['成交量(亿)']:.2f}亿")
            print(f"涨停/跌停: {result['涨停家数']}/{result['跌停家数']}")
            print(f"上涨/下跌: {result['上涨家数']}/{result['下跌家数']}")
            
            return result
        else:
            print(f"未获取到{date}的市场数据")
            return None
            
    except Exception as e:
        print(f"获取市场数据失败: {str(e)}")
        return None


def get_recent_market_data(days=10):
    """获取最近n天的市场数据"""
    results = []
    end_date = datetime.now()
    
    for i in range(days):
        date = (end_date - timedelta(days=i)).strftime('%Y%m%d')
        data = get_market_data(date)
        if data:
            results.append(data)
    
    if results:
        # 转换为DataFrame并保存
        df = pd.DataFrame(results)
        df.to_excel('hushen_market_data.xlsx', index=False)
        print('\n数据已保存到 hushen_market_data.xlsx')
        return df
    else:
        print("未获取到任何数据")
        return None


if __name__ == "__main__":
    # 获取最近10天的数据
    get_recent_market_data(10)
