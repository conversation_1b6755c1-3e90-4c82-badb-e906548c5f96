#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Z哥战法每日自动选股脚本
每个交易日17:00自动运行，更新数据并进行选股
"""

import os
import sys
import subprocess
import datetime
import logging
from pathlib import Path
import json
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('daily_selection.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def is_trading_day():
    """
    简单判断是否为交易日（周一到周五，排除节假日需要手动维护）
    """
    today = datetime.date.today()
    weekday = today.weekday()  # 0=Monday, 6=Sunday
    
    # 排除周末
    if weekday >= 5:  # Saturday=5, Sunday=6
        return False
    
    # 这里可以添加节假日判断逻辑
    # 简单起见，暂时只排除周末
    return True

def run_command(command, description):
    """
    运行命令并返回结果
    """
    logger.info(f"开始执行: {description}")
    logger.info(f"命令: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=1800  # 30分钟超时
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} 执行成功")
            return True, result.stdout
        else:
            logger.error(f"❌ {description} 执行失败")
            logger.error(f"错误输出: {result.stderr}")
            return False, result.stderr
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ {description} 执行超时")
        return False, "执行超时"
    except Exception as e:
        logger.error(f"❌ {description} 执行异常: {e}")
        return False, str(e)

def update_data():
    """
    更新股票数据
    """
    # 使用完整Python路径和增量更新
    python_path = r'C:\ProgramData\anaconda3\python.exe'
    command = f'"{python_path}" fetch_kline.py --datasource tushare --frequency 4 --exclude-gem True --min-mktcap 5e9 --max-mktcap +inf --start today --end today --out ./data --workers 3'
    return run_command(command, "更新股票数据")

def run_stock_selection():
    """
    运行选股程序
    """
    python_path = r'C:\ProgramData\anaconda3\python.exe'
    command = f'"{python_path}" select_stock.py --data-dir ./data --config ./configs.json'
    return run_command(command, "执行选股程序")

def parse_selection_results(output):
    """
    解析选股结果
    """
    results = {}
    current_strategy = None

    if output is None:
        output = ""

    lines = output.split('\n')
    for line in lines:
        line = line.strip()
        
        # 匹配策略名称
        strategy_match = re.search(r'============== 选股结果 \[(.+?)\] ==============', line)
        if strategy_match:
            current_strategy = strategy_match.group(1)
            continue
            
        # 匹配交易日
        date_match = re.search(r'交易日: (\d{4}-\d{2}-\d{2})', line)
        if date_match and current_strategy:
            trade_date = date_match.group(1)
            if current_strategy not in results:
                results[current_strategy] = {'date': trade_date, 'count': 0, 'stocks': []}
            continue
            
        # 匹配股票数量
        count_match = re.search(r'符合条件股票数: (\d+)', line)
        if count_match and current_strategy:
            results[current_strategy]['count'] = int(count_match.group(1))
            continue
            
        # 匹配股票代码
        if current_strategy and ',' in line and re.match(r'^[0-9,\s]+$', line):
            stocks = [stock.strip() for stock in line.split(',') if stock.strip()]
            results[current_strategy]['stocks'] = stocks
            continue
            
        # 匹配无符合条件股票
        if '无符合条件股票' in line and current_strategy:
            results[current_strategy]['stocks'] = []
            continue
    
    return results

def update_markdown_report(results):
    """
    更新markdown报告
    """
    markdown_file = Path('daily_selection_report.md')
    today = datetime.date.today().strftime('%Y-%m-%d')
    
    # 准备新的报告内容
    new_content = f"\n## {today} 选股结果\n\n"
    new_content += f"**更新时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    total_stocks = 0
    for strategy, data in results.items():
        count = data['count']
        stocks = data['stocks']
        total_stocks += count
        
        new_content += f"### {strategy}\n"
        new_content += f"- **符合条件股票数**: {count}只\n"
        
        if stocks:
            # 每行显示10只股票
            stock_lines = []
            for i in range(0, len(stocks), 10):
                stock_line = ', '.join(stocks[i:i+10])
                stock_lines.append(stock_line)
            new_content += f"- **股票代码**: \n"
            for line in stock_lines:
                new_content += f"  - {line}\n"
        else:
            new_content += f"- **股票代码**: 无符合条件股票\n"
        new_content += "\n"
    
    new_content += f"**今日总计**: {total_stocks}只股票符合条件\n\n"
    new_content += "---\n"
    
    # 读取现有内容或创建新文件
    if markdown_file.exists():
        with open(markdown_file, 'r', encoding='utf-8') as f:
            existing_content = f.read()
    else:
        existing_content = "# Z哥战法每日选股报告\n\n"
        existing_content += "本报告记录每个交易日的选股结果，按时间倒序排列。\n\n"
        existing_content += "## 策略说明\n\n"
        existing_content += "- **少妇战法**: BBI上升 + KDJ低位 + MACD金叉\n"
        existing_content += "- **补票战法**: BBI上升 + 短长期RSV条件 + MACD金叉\n"
        existing_content += "- **TePu战法**: 放量突破 + KDJ低位 + MACD金叉\n"
        existing_content += "- **填坑战法**: 峰值回调 + KDJ低位\n\n"
        existing_content += "---\n\n"
    
    # 将新内容插入到现有内容的开头（在标题和说明之后）
    lines = existing_content.split('\n')
    insert_index = -1
    for i, line in enumerate(lines):
        if line.strip() == '---' and i > 5:  # 找到第一个分隔线
            insert_index = i + 1
            break
    
    if insert_index > 0:
        lines.insert(insert_index, new_content)
        final_content = '\n'.join(lines)
    else:
        final_content = existing_content + new_content
    
    # 写入文件
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    logger.info(f"✅ 报告已更新到 {markdown_file}")

def main():
    """
    主函数
    """
    logger.info("=" * 60)
    logger.info("Z哥战法每日自动选股开始")
    logger.info(f"当前时间: {datetime.datetime.now()}")
    
    # 检查是否为交易日
    if not is_trading_day():
        logger.info("今天不是交易日，跳过执行")
        return
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    logger.info(f"工作目录: {script_dir}")
    
    try:
        # 步骤1: 更新数据
        logger.info("步骤1: 更新股票数据")
        success, output = update_data()
        if not success:
            logger.warning("数据更新失败，但继续使用现有数据进行选股")
            logger.warning(f"更新失败原因: {output}")
        else:
            logger.info("数据更新成功")
        
        # 步骤2: 运行选股
        logger.info("步骤2: 执行选股程序")
        success, output = run_stock_selection()
        if not success:
            logger.error("选股程序执行失败，终止执行")
            return
        
        # 步骤3: 解析结果
        logger.info("步骤3: 解析选股结果")
        results = parse_selection_results(output)
        
        if not results:
            logger.warning("未能解析到选股结果")
            return
        
        # 步骤4: 更新报告
        logger.info("步骤4: 更新markdown报告")
        update_markdown_report(results)
        
        # 输出简要结果
        logger.info("选股结果摘要:")
        total = 0
        for strategy, data in results.items():
            count = data['count']
            total += count
            logger.info(f"  {strategy}: {count}只")
        logger.info(f"  总计: {total}只")
        
        logger.info("✅ 每日选股任务完成")
        
    except Exception as e:
        logger.error(f"❌ 执行过程中发生异常: {e}")
        raise
    
    finally:
        logger.info("Z哥战法每日自动选股结束")
        logger.info("=" * 60)

if __name__ == "__main__":
    main()
