import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import pywenca<PERSON>


def get_recent_trading_days(n=10):
    """获取最近n个交易日（含今天）"""
    trade_date_df = ak.tool_trade_date_hist_sina()
    trade_date_df['trade_date'] = pd.to_datetime(trade_date_df['trade_date'])
    today = datetime.now().date()
    
    # 只保留今天及之前的交易日，并按日期降序排序
    filtered_df = trade_date_df[trade_date_df['trade_date'].dt.date <= today]
    sorted_df = filtered_df.sort_values('trade_date', ascending=False)
    
    # 获取最近n天的日期并转换格式
    dates = []
    for _, row in sorted_df.head(n).iterrows():
        dates.append(row['trade_date'].strftime('%Y-%m-%d'))
    return dates[::-1]  # 升序返回


def get_limit_up_data(date):
    """获取涨停数据"""
    try:
        param = f"非ST,{date}涨停"
        df = pywencai.get(query=param, sort_key='成交金额', sort_order='desc', loop=True)
        return df
    except Exception as e:
        print(f"获取涨停数据失败: {str(e)}")
        return pd.DataFrame()


def get_limit_down_data(date):
    """获取跌停数据"""
    try:
        param = f"非ST,{date}跌停"
        df = pywencai.get(query=param, sort_key='成交金额', sort_order='desc', loop=True)
        return df
    except Exception as e:
        print(f"获取跌停数据失败: {str(e)}")
        return pd.DataFrame()


def get_index_data(symbol, date):
    """获取指数数据"""
    try:
        df = ak.stock_zh_index_daily_em(symbol=symbol)
        df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
        
        day_data = df[df['date'] == date]
        if day_data.empty:
            return None
            
        idx = list(day_data.index)[0]
        current_close = day_data.iloc[0]['close']
        
        # 计算涨跌幅
        if idx > 0:  # 确保不是第一条数据
            last_close = df.iloc[idx - 1]['close']
            change = (current_close - last_close) / last_close * 100
        else:
            change = 0
            
        return {'close': current_close, 'change': change}
        
    except Exception as e:
        print(f"获取指数数据失败: {str(e)}")
        return None


def get_market_data():
    """获取市场综合数据"""
    trading_days = get_recent_trading_days(10)
    print(f"获取到的交易日列表: {trading_days}")
    
    result = []
    for day in trading_days:
        print(f"\n处理日期: {day}")
        
        # 获取上证指数数据
        sh_data = get_index_data("sh000001", day)
        # 获取创业板指数据
        sz_data = get_index_data("sz399006", day)
        
        # 获取涨跌停数据
        limit_up_df = get_limit_up_data(day)
        limit_down_df = get_limit_down_data(day)
        
        # 获取市场涨跌数据
        try:
            market_query = f"{day}上涨家数，下跌家数，成交额"
            market_data = pywencai.get(query=market_query, loop=True)
            
            if market_data.empty:
                print(f"未获取到{day}的市场涨跌数据")
                continue
                
            # 构建当日数据字典
            day_data = {
                '日期': day,
                '上证指数': sh_data['close'] if sh_data else 0,
                '上证涨跌幅%': sh_data['change'] if sh_data else 0,
                '创业板指': sz_data['close'] if sz_data else 0,
                '创业板涨跌幅%': sz_data['change'] if sz_data else 0,
                '沪深总成交(亿)': float(market_data.loc[0, '成交额']) / 100 if not market_data.empty else 0,
                '上涨家数': int(market_data.loc[0, '上涨家数']) if not market_data.empty else 0,
                '下跌家数': int(market_data.loc[0, '下跌家数']) if not market_data.empty else 0,
                '涨停数': len(limit_up_df) if not limit_up_df.empty else 0,
                '跌停数': len(limit_down_df) if not limit_down_df.empty else 0
            }
            
            result.append(day_data)
            print(f"涨停数: {len(limit_up_df)}, 跌停数: {len(limit_down_df)}")
            print(f"上涨数: {day_data['上涨家数']}, 下跌数: {day_data['下跌家数']}")
            print(f"成交额: {day_data['沪深总成交(亿)']:.2f}亿")
            
        except Exception as e:
            print(f"处理日期 {day} 时发生错误: {str(e)}")
            continue
    
    if not result:
        print("警告：未获取到任何数据！")
        return
        
    df = pd.DataFrame(result)
    print("\n最终数据预览:")
    print(df)
    
    # 保存到Excel
    df.to_excel('market_data_last10days.xlsx', index=False)
    print('\n数据已保存到 market_data_last10days.xlsx')

if __name__ == "__main__":
    get_market_data()