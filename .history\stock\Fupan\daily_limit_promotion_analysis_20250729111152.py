# ==================== 涨停晋级分析工具 ====================
# 功能：智能标注Excel中的涨停股票表现，创建晋级文件
# 核心逻辑：sheet名字 → 当日日期 → 下一交易日 → 查询数据 → 智能标注 → 晋级文件
# 作者：AI Assistant
# 日期：2025-07-28
# ========================================================

import akshare as ak
import pandas as pd
import pywencai
import os
from datetime import datetime
from openpyxl.styles import PatternFill, Font, Border, Side
import openpyxl

# ==================== 日期处理函数 ====================

def get_next_trading_date(current_date):
    """获取下一个交易日：验证当前日期是交易日，然后从交易日历获取下一交易日"""
    try:
        # 直接获取交易日历
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])

        if trade_dates_df.empty:
            raise Exception("无法获取交易日历数据")

        # 转换为日期列表
        trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
        trading_dates = sorted(trading_dates)

        # 验证当前日期是否为交易日
        if current_date not in trading_dates:
            raise Exception(f"{current_date.strftime('%Y-%m-%d')} 不是交易日")

        # 从交易日历获取下一交易日
        current_index = trading_dates.index(current_date)
        if current_index + 1 >= len(trading_dates):
            raise Exception(f"交易日历中没有 {current_date.strftime('%Y-%m-%d')} 之后的交易日")

        next_trading_date = trading_dates[current_index + 1]
        print(f"📅 {current_date.strftime('%m%d')} → {next_trading_date.strftime('%m%d')}")
        return next_trading_date

    except Exception as e:
        print(f"❌ 交易日获取失败: {str(e)}")
        raise e


# ==================== Excel处理函数 ====================

def extract_all_stock_names_from_excel(df):
    """从Excel提取股票名称"""
    stock_names = set()

    # 遍历所有单元格（跳过第一行第一列，因为通常是标题）
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            # 跳过第一行第一列
            if row_idx == 0 and col_idx == 0:
                continue

            cell_value = df.iloc[row_idx, col_idx]

            # 转换为字符串并清理
            if pd.notna(cell_value):
                cell_str = str(cell_value).strip()

                # 过滤掉明显不是股票名称的内容
                if (len(cell_str) >= 2 and len(cell_str) <= 10 and
                    cell_str != '' and cell_str != 'nan' and
                    not cell_str.isdigit() and  # 不是纯数字
                    not cell_str.replace('.', '').replace('%', '').replace('-', '').isdigit()):  # 不是数字格式

                    # 进一步过滤：排除常见的非股票名称
                    exclude_keywords = ['合计', '小计', '总计', '备注', '说明', '分类', '类别',
                                      '日期', '时间', '序号', '编号', '行业', '板块']

                    if not any(keyword in cell_str for keyword in exclude_keywords):
                        stock_names.add(cell_str)

    # 转换为列表并排序
    stock_names_list = sorted(list(stock_names))
    return stock_names_list

# ==================== 数据查询函数 ====================

def get_optimized_performance_data(current_date, next_trading_date):
    """优化的获取当日涨停股票次日表现逻辑，同时获取当日涨停股票的连板信息"""
    try:
        current_str = current_date.strftime('%Y%m%d')
        next_str = next_trading_date.strftime('%Y%m%d')

        print(f"🔍 查询{current_str}→{next_str}表现数据...")

        # 获取当日涨停股票及连板信息
        current_limit_up_dict = {}
        try:
            current_query = f"非ST,沪深主板,{current_str}涨停"
            current_df = pywencai.get(query=current_query, loop=True)
            if current_df is not None and not current_df.empty:
                continuous_col = None
                for col in current_df.columns:
                    if '连续涨停天数' in str(col):
                        continuous_col = col
                        break

                if continuous_col and '股票简称' in current_df.columns:
                    for _, row in current_df.iterrows():
                        stock_name = row['股票简称']
                        try:
                            continuous_days = int(row[continuous_col])
                            current_limit_up_dict[stock_name] = continuous_days
                        except (ValueError, TypeError):
                            current_limit_up_dict[stock_name] = 1
        except Exception as e:
            print(f"⚠️ 当日涨停查询失败: {str(e)}")

        # 获取次日继续涨停的股票
        continue_limit_up_names = set()
        try:
            continue_query = f"{current_str}涨停,沪深主板,非ST,{next_str}涨停"
            continue_limit_up_df = pywencai.get(query=continue_query, loop=True)
            if continue_limit_up_df is not None and not continue_limit_up_df.empty:
                continue_limit_up_names = set(continue_limit_up_df['股票简称'].tolist())
        except Exception as e:
            print(f"⚠️ 继续涨停查询失败: {str(e)}")

        # 获取次日跌停的股票
        limit_down_names = set()
        try:
            limit_down_query = f"{current_str}涨停,沪深主板,非ST,{next_str}跌停"
            limit_down_df = pywencai.get(query=limit_down_query, loop=True)
            if limit_down_df is not None and not limit_down_df.empty:
                limit_down_names = set(limit_down_df['股票简称'].tolist())
        except Exception as e:
            print(f"⚠️ 跌停查询失败: {str(e)}")

        # 获取涨跌幅数据
        performance_query = f"{current_str}涨停,沪深主板,非ST,{next_str}涨跌幅"
        performance_df = pywencai.get(query=performance_query, loop=True)

        if performance_df is None or performance_df.empty:
            print("❌ 获取涨跌幅数据失败")
            return None, set(), set(), {}

        # 验证关键列
        if '股票简称' not in performance_df.columns:
            print("❌ 缺少股票简称列")
            return None, set(), set(), {}

        print(f"✅ 当日涨停:{len(current_limit_up_dict)} 继续涨停:{len(continue_limit_up_names)} 跌停:{len(limit_down_names)} 总计:{len(performance_df)}")

        return performance_df, continue_limit_up_names, limit_down_names, current_limit_up_dict

    except Exception as e:
        print(f"❌ 获取表现数据失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return None, set(), set(), {}

def get_first_board_stocks_data(next_trading_date):
    """获取下一交易日首板股票数据（连续涨停天数=1）"""
    try:
        date_str = next_trading_date.strftime('%Y%m%d')
        query = f"非ST,沪深主板,{date_str}涨停,连续涨停天数=1,涨停原因类别,几天几板,首次涨停时间,最终涨停时间,换手率,流通市值"

        df = pywencai.get(query=query, loop=True)
        if df is None or df.empty:
            return None

        # 字段映射（删除开板次数和涨停类型）
        field_keywords = {
            '股票简称': '股票简称',
            '涨停原因类别': '涨停原因类别',
            '连续涨停天数': '连板',
            '几天几板': '几天几板',
            '首次涨停时间': '首次涨停',
            '最终涨停时间': '最终涨停',
            '换手率': '换手率',
            'a股流通市值': '流通市值',
            'a股市值(不含限售股)': '流通市值'
        }

        # 查找可用字段
        available_fields = {}
        for keyword, new_name in field_keywords.items():
            for col in df.columns:
                if keyword in str(col):
                    available_fields[col] = new_name
                    break

        if not available_fields:
            return None

        # 处理数据
        processed_data = []
        for _, row in df.iterrows():
            row_data = {}
            for original_field, new_field in available_fields.items():
                value = row.get(original_field, '')

                # 特殊处理
                if '流通市值' in new_field:
                    # 转换为亿元
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).replace(',', '').replace('亿', '').replace('万', '').replace('元', '')
                            numeric_value = float(value_str)
                            if '亿' in str(value):
                                row_data[new_field] = round(numeric_value, 2)
                            elif '万' in str(value):
                                row_data[new_field] = round(numeric_value / 10000, 2)
                            else:
                                row_data[new_field] = round(numeric_value / 100000000, 2)
                        else:
                            row_data[new_field] = ''
                    except (ValueError, TypeError):
                        row_data[new_field] = ''
                elif '最终涨停' in new_field or '首次涨停' in new_field:
                    # 时间格式化为 HH:MM
                    try:
                        if pd.notna(value) and value != '':
                            time_str = str(value)
                            if ' ' in time_str:
                                time_str = time_str.split(' ')[-1]
                            if ':' in time_str:
                                parts = time_str.split(':')
                                if len(parts) >= 2:
                                    hour = int(parts[0])
                                    minute = int(parts[1])
                                    row_data[new_field] = f"{hour:02d}:{minute:02d}"
                                else:
                                    row_data[new_field] = time_str
                            else:
                                row_data[new_field] = time_str
                        else:
                            row_data[new_field] = ''
                    except (ValueError, TypeError):
                        row_data[new_field] = ''
                elif '换手率' in new_field:
                    # 百分比格式
                    try:
                        if pd.notna(value) and value != '':
                            value_str = str(value).replace(',', '').replace('%', '')
                            if value_str.lower() in ['--', '-', 'nan', 'null', '']:
                                row_data[new_field] = ''
                            else:
                                numeric_value = float(value_str)
                                if '%' in str(value):
                                    row_data[new_field] = round(numeric_value, 1)
                                else:
                                    if numeric_value <= 1:
                                        row_data[new_field] = round(numeric_value * 100, 1)
                                    else:
                                        row_data[new_field] = round(numeric_value, 1)
                        else:
                            row_data[new_field] = ''
                    except (ValueError, TypeError):
                        row_data[new_field] = ''
                else:
                    row_data[new_field] = value if pd.notna(value) else ''

            processed_data.append(row_data)

        # 转换为DataFrame并排序
        result_df = pd.DataFrame(processed_data)
        if result_df.empty:
            return None

        # 排序：先按几天几板，再按最终涨停时间
        def time_to_minutes(time_str):
            try:
                if pd.notna(time_str) and time_str != '':
                    parts = str(time_str).split(':')
                    if len(parts) == 2:
                        return int(parts[0]) * 60 + int(parts[1])
                return 9999  # 无效时间排到最后
            except:
                return 9999

        # 添加时间排序辅助列
        if '最终涨停时间' in result_df.columns:
            result_df['_sort_time'] = result_df['最终涨停时间'].apply(time_to_minutes)
        else:
            result_df['_sort_time'] = 9999

        # 排序：几天几板（升序） -> 最终涨停时间（升序，早的在前）
        sort_columns = []
        sort_ascending = []

        if '几天几板' in result_df.columns:
            sort_columns.append('几天几板')
            sort_ascending.append(True)

        sort_columns.append('_sort_time')
        sort_ascending.append(True)

        result_df = result_df.sort_values(sort_columns, ascending=sort_ascending)
        result_df = result_df.drop('_sort_time', axis=1)

        return result_df

    except Exception as e:
        print(f"❌ 首板股票查询失败: {str(e)}")
        return None

# ==================== 样式处理函数 ====================

def get_gradient_color(change_pct, is_limit_up=False, is_limit_down=False):
    """根据涨跌幅获取美化渐变颜色，每1%精细渐变"""

    # 涨停跌停特殊处理
    if is_limit_up:
        return "limit_up"  # 涨停：红色加粗字体
    elif is_limit_down:
        return "limit_down"  # 跌停：绿色加粗字体

    # 限制范围在-10%到10%之间
    change_pct = max(-10, min(10, change_pct))

    # 使用更美观的颜色方案：橙红色系 vs 青蓝色系
    if change_pct >= 0:
        # 上涨：从浅橙到深橙红 (0% -> 10%)
        # 使用HSL色彩空间实现更自然的渐变
        ratio = change_pct / 10.0  # 0到1

        # 橙红色系：从 #FFF5F5 (浅粉橙) 到 #FF4500 (橙红)
        # 基础色：橙红 (255, 69, 0)
        red = 255
        green = int(245 - (245 - 69) * ratio)  # 245 -> 69
        blue = int(245 - 245 * ratio)          # 245 -> 0

    else:
        # 下跌：从浅青到深青蓝 (0% -> -10%)
        ratio = abs(change_pct) / 10.0  # 0到1

        # 青蓝色系：从 #F0F8FF (浅青) 到 #008B8B (深青)
        # 基础色：深青 (0, 139, 139)
        red = int(240 - 240 * ratio)           # 240 -> 0
        green = int(248 - (248 - 139) * ratio) # 248 -> 139
        blue = int(255 - (255 - 139) * ratio)  # 255 -> 139

    # 转换为16进制颜色
    color_hex = f"{red:02X}{green:02X}{blue:02X}"
    return f"gradient_{color_hex}"

def apply_cell_style(cell, style_code, limit_up_font, limit_down_font):
    """统一的样式应用函数"""
    if style_code == "limit_up":
        cell.font = limit_up_font
    elif style_code == "limit_down":
        cell.font = limit_down_font
    elif style_code.startswith("gradient_"):
        color_hex = style_code.replace("gradient_", "")
        cell.fill = PatternFill(start_color=color_hex, end_color=color_hex, fill_type="solid")

def apply_conditional_formatting_to_first_board(worksheet, data_df, headers, start_col, title_row):
    """
    为首板股票数据应用条件格式（参考daily_limit_precisequery.py）

    Args:
        worksheet: Excel工作表对象
        data_df: 首板股票数据DataFrame
        headers: 列标题列表
        start_col: 起始列索引
        title_row: 标题行索引
    """
    try:
        # 最终涨停时间条件格式
        if '最终涨停' in headers:
            final_limit_col_idx = headers.index('最终涨停')
            final_limit_col = start_col + final_limit_col_idx
            time_format_count = 0

            for row_idx in range(title_row + 1, title_row + 1 + len(data_df)):
                try:
                    data_row_idx = row_idx - title_row - 1
                    if data_row_idx >= len(data_df) or '最终涨停' not in data_df.columns:
                        continue

                    final_time = data_df.iloc[data_row_idx]['最终涨停']
                    if pd.notna(final_time) and final_time != '':
                        time_str = str(final_time)
                        if ':' in time_str:
                            try:
                                # 提取时间部分（如果包含日期）
                                if ' ' in time_str:
                                    time_str = time_str.split(' ')[-1]

                                hour, minute = map(int, time_str.split(':')[:2])
                                time_minutes = hour * 60 + minute
                                cell = worksheet.cell(row=row_idx, column=final_limit_col)

                                # 9:25及之前：深红色
                                if time_minutes <= 565:
                                    cell.fill = PatternFill(start_color="CC0000", end_color="CC0000", fill_type="solid")
                                    cell.font = Font(color="FFFFFF", bold=True)
                                    time_format_count += 1
                                # 9:30-10:00：渐变红色
                                elif 570 <= time_minutes <= 600:
                                    progress = (time_minutes - 570) / 30
                                    colors = ["FF3333", "FF4D4D", "FF6666", "FF8080", "FF9999", "FFB3B3", "FFCCCC", "FFE6E6"]
                                    color_index = min(int(progress * len(colors)), len(colors) - 1)
                                    color = colors[color_index]
                                    cell.fill = PatternFill(start_color=color, end_color=color, fill_type="solid")
                                    cell.font = Font(color="000000")
                                    time_format_count += 1
                            except (ValueError, TypeError, IndexError):
                                continue
                except:
                    continue

        # 流通市值条件格式
        if '流通市值' in headers:
            market_value_col_idx = headers.index('流通市值')
            market_value_col = start_col + market_value_col_idx
            market_format_count = 0

            for row_idx in range(title_row + 1, title_row + 1 + len(data_df)):
                try:
                    data_row_idx = row_idx - title_row - 1
                    if data_row_idx >= len(data_df) or '流通市值' not in data_df.columns:
                        continue

                    market_value = data_df.iloc[data_row_idx]['流通市值']
                    if pd.notna(market_value) and market_value != '':
                        try:
                            market_value_float = float(market_value)
                            cell = worksheet.cell(row=row_idx, column=market_value_col)

                            # 根据流通市值设置不同深度的蓝色
                            if 200 <= market_value_float < 500:
                                cell.fill = PatternFill(start_color="E6F3FF", end_color="E6F3FF", fill_type="solid")
                                market_format_count += 1
                            elif 500 <= market_value_float < 1000:
                                cell.fill = PatternFill(start_color="B3D9FF", end_color="B3D9FF", fill_type="solid")
                                market_format_count += 1
                            elif market_value_float >= 1000:
                                cell.fill = PatternFill(start_color="4D94FF", end_color="4D94FF", fill_type="solid")
                                cell.font = Font(color="FFFFFF")
                                market_format_count += 1
                        except (ValueError, TypeError):
                            continue
                except:
                    continue

        # 换手率高风险标注
        if '换手率' in headers and '流通市值' in headers:
            turnover_col_idx = headers.index('换手率')
            turnover_col = start_col + turnover_col_idx
            turnover_format_count = 0

            for row_idx in range(title_row + 1, title_row + 1 + len(data_df)):
                try:
                    data_row_idx = row_idx - title_row - 1
                    if data_row_idx >= len(data_df) or '换手率' not in data_df.columns or '流通市值' not in data_df.columns:
                        continue

                    market_value = data_df.iloc[data_row_idx]['流通市值']
                    turnover = data_df.iloc[data_row_idx]['换手率']

                    if pd.notna(market_value) and pd.notna(turnover) and market_value != '' and turnover != '':
                        try:
                            market_value_float = float(market_value)
                            turnover_float = float(turnover)

                            # 根据流通市值确定高风险阈值
                            if market_value_float < 20:
                                threshold = 25.0
                            elif market_value_float <= 50:
                                threshold = 20.0
                            elif market_value_float <= 100:
                                threshold = 15.0
                            else:
                                threshold = 12.0

                            # 如果换手率超过阈值，标注为黄色
                            if turnover_float > threshold:
                                cell = worksheet.cell(row=row_idx, column=turnover_col)
                                cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
                                turnover_format_count += 1
                        except (ValueError, TypeError):
                            continue
                except:
                    continue

    except Exception as e:
        print(f"⚠️ 条件格式应用失败: {str(e)}")

def process_optimized_performance_data(performance_df, continue_limit_up_names, limit_down_names):
    """处理优化后的表现数据，使用条件格式"""
    try:
        result = []

        for _, row in performance_df.iterrows():
            stock_name = row['股票简称']

            # 获取涨跌幅（支持动态日期格式）
            today_change = 0
            found_change_col = None

            # 先尝试精确匹配
            exact_cols = ['涨跌幅', '涨跌幅%', '涨跌幅(%)', '涨跌', '涨跌率']
            for col in exact_cols:
                if col in row.index:
                    try:
                        change_value = row[col]
                        if isinstance(change_value, str):
                            today_change = float(change_value.replace('%', ''))
                        else:
                            today_change = float(change_value)
                        found_change_col = col
                        break
                    except (ValueError, TypeError):
                        continue

            # 如果精确匹配失败，尝试模糊匹配（支持日期格式）
            if found_change_col is None:
                change_keywords = ['涨跌幅', '涨跌', '涨跌率']
                for col in row.index:
                    col_str = str(col)
                    if any(keyword in col_str for keyword in change_keywords):
                        try:
                            change_value = row[col]
                            if isinstance(change_value, str):
                                today_change = float(change_value.replace('%', ''))
                            else:
                                today_change = float(change_value)
                            found_change_col = col
                            break
                        except (ValueError, TypeError):
                            continue

            # 判断是否涨停或跌停（直接从查询结果判断）
            is_limit_up = stock_name in continue_limit_up_names
            is_limit_down = stock_name in limit_down_names

            # 获取渐变颜色
            style_code = get_gradient_color(today_change, is_limit_up, is_limit_down)

            # 确定状态描述
            if is_limit_up:
                status = "涨停"
            elif is_limit_down:
                status = "跌停"
            else:
                # 直接显示涨跌幅，不分类
                if today_change > 0:
                    status = f"+{today_change:.2f}%"
                elif today_change < 0:
                    status = f"{today_change:.2f}%"
                else:
                    status = "0.00%"

            result.append({
                '股票简称': stock_name,
                '昨日状态': '涨停',
                '次日涨跌幅': f"{today_change:.2f}%",
                '次日状态': status,
                '标注样式': style_code
            })

        result_df = pd.DataFrame(result)

        # 简化结果统计
        if not result_df.empty:
            style_counts = result_df['标注样式'].value_counts()
            limit_up_count = len([s for s in style_counts.index if s == "limit_up"])
            limit_down_count = len([s for s in style_counts.index if s == "limit_down"])

            print(f"✅ 处理完成: 涨停{limit_up_count} 跌停{limit_down_count} 总计{len(result_df)}")

        return result_df

    except Exception as e:
        print(f"❌ 处理表现数据失败: {str(e)}")
        return None


# 删除get_first_board_stocks_data函数，将移动到数据查询函数区域

# 函数体已删除，将移动到数据查询函数区域

# ==================== 主要业务函数 ====================

def annotate_and_create_promotion_excel(input_file_path, sheet_name, performance_data, output_file_path=None, promotion_suffix="晋级", current_limit_up_dict=None):
    """在Excel文件中标注股票表现并同时创建晋级sheet"""
    try:
        if output_file_path is None:
            output_file_path = input_file_path

        print(f"📝 处理: {sheet_name} → {promotion_suffix}")

        # 核心日期处理逻辑
        current_date = parse_sheet_date(sheet_name)  # 当日：如0724 → 2025-07-24
        if not current_date:
            print("⚠️ 无法解析工作表日期，跳过处理")
            return False, None

        try:
            next_trading_date = get_next_trading_date(current_date)  # 明日：2025-07-25
        except Exception as e:
            print(f"⚠️ 日期处理失败: {str(e)}")
            return False, None

        # 获取数据
        performance_df, continue_limit_up_names, limit_down_names, current_limit_up_dict = get_optimized_performance_data(current_date, next_trading_date)

        if not current_limit_up_dict:
            print("⚠️ 未获取到当日涨停股票数据")
            return False, None

        # 从连板数据中提取当日涨停股票名称
        current_limit_up_stocks = set(current_limit_up_dict.keys())

        # 读取Excel文件
        workbook = openpyxl.load_workbook(input_file_path)
        worksheet = workbook[sheet_name]

        # 创建字体样式（参考reviewtracker）
        limit_up_font = Font(
            name="Microsoft YaHei",  # 微软雅黑字体
            size=14,                 # 加大字体
            color="CC0000",          # 深红色
            bold=True,               # 加粗
            italic=False             # 不倾斜
        )

        limit_down_font = Font(
            name="Microsoft YaHei",  # 微软雅黑字体
            size=14,                 # 加大字体
            color="006600",          # 深绿色
            bold=True,               # 加粗
            italic=False             # 不倾斜
        )

        # 创建股票表现字典
        performance_dict = {}
        limit_up_stocks = set()
        all_stocks = set()

        if performance_data is not None and not performance_data.empty:
            for _, row in performance_data.iterrows():
                stock_name = row['股票简称']
                all_stocks.add(stock_name)
                performance_dict[stock_name] = {
                    '次日涨跌幅': row['次日涨跌幅'],
                    '次日状态': row['次日状态'],
                    '标注样式': row['标注样式']
                }
                if row.get('标注样式') == 'limit_up':
                    limit_up_stocks.add(stock_name)

        print(f"📊 准备标注 {len(performance_dict)} 只股票")
        print(f"🔥 其中涨停股票 {len(limit_up_stocks)} 只")

        # 统计标注情况
        annotated_count = 0
        found_stocks = set()  # 记录在Excel中找到的股票

        # 遍历所有单元格查找股票名称并标注
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value:
                    cell_value = str(cell.value).strip()

                    # 检查是否为股票简称（精确匹配）
                    if cell_value in performance_dict:
                        stock_info = performance_dict[cell_value]
                        found_stocks.add(cell_value)

                        # 只对股票简称本身应用样式，不添加右侧信息
                        apply_cell_style(cell, stock_info['标注样式'], limit_up_font, limit_down_font)

                        annotated_count += 1

        print(f"✅ 成功标注 {annotated_count} 只股票")

        # 检查是否有未在Excel中出现的股票
        excel_stocks = found_stocks

        missing_stocks = set(performance_dict.keys()) - excel_stocks

        # 检查当日涨停股票是否在Excel中
        excel_missing_limit_up = current_limit_up_stocks - excel_stocks

        # 合并所有需要添加的股票
        all_missing_stocks = missing_stocks | excel_missing_limit_up

        if all_missing_stocks:
            print(f"⚠️ 发现需要补充的股票:")
            if missing_stocks:
                print(f"   表现数据中缺失: {len(missing_stocks)} 只")
                print(f"   {', '.join(sorted(missing_stocks))}")
            if excel_missing_limit_up:
                print(f"   {current_date.strftime('%Y-%m-%d')} 涨停但Excel中未发现: {len(excel_missing_limit_up)} 只")
                print(f"   {', '.join(sorted(excel_missing_limit_up))}")
                print(f"🔍 这些股票来自 {current_date.strftime('%Y-%m-%d')} 的涨停数据，不是最新交易日")

            # 使用从get_optimized_performance_data获取的连板数据
            continuous_dict = current_limit_up_dict if current_limit_up_dict else {}

            # 参考reviewtracker的逻辑：添加到最后的新列中
            print("🔄 自动添加缺失股票到Excel最后列...")

            # 找到有数据的最后一列
            max_col = 1
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        max_col = max(max_col, cell.column)

            # 在最后一列后面添加两列标题：股票简称和连板数（参考reviewtracker）
            next_col = max_col + 1

            # 第一列：股票简称标题
            date_str = current_date.strftime('%m%d') if current_date else "昨日"
            title_cell1 = worksheet.cell(row=1, column=next_col, value=f"未在表中的{date_str}涨停股票")
            title_cell1.font = Font(bold=True)

            # 第二列：连板数标题
            title_cell2 = worksheet.cell(row=1, column=next_col + 1, value="连板数")
            title_cell2.font = Font(bold=True)

            # 添加缺失股票，分为两列
            added_count = 0
            row_index = 2  # 从第二行开始

            # 添加有表现数据的缺失股票
            for stock_name in sorted(missing_stocks):
                stock_info = performance_dict[stock_name]

                # 获取连板数
                continuous_days = continuous_dict.get(stock_name, 1)

                # 第一列：股票简称
                name_cell = worksheet.cell(row=row_index, column=next_col, value=stock_name)
                apply_cell_style(name_cell, stock_info['标注样式'], limit_up_font, limit_down_font)

                # 第二列：连板数（不应用特殊样式，保持默认格式）
                worksheet.cell(row=row_index, column=next_col + 1, value=continuous_days)

                row_index += 1
                added_count += 1

            # 添加当日涨停但Excel中未发现的股票
            for stock_name in sorted(excel_missing_limit_up):
                if stock_name not in missing_stocks:  # 避免重复添加
                    # 获取连板数
                    continuous_days = continuous_dict.get(stock_name, 1)

                    # 第一列：股票简称
                    name_cell = worksheet.cell(row=row_index, column=next_col, value=stock_name)
                    apply_cell_style(name_cell, 'limit_up', limit_up_font, limit_down_font)

                    # 第二列：连板数（不应用特殊样式，保持默认格式）
                    worksheet.cell(row=row_index, column=next_col + 1, value=continuous_days)

                    row_index += 1
                    added_count += 1

            print(f"✅ 已在新列中添加 {added_count} 只缺失股票（已标注连板数）")

            # 更新涨停股票集合
            limit_up_stocks.update(excel_missing_limit_up)

        # 创建晋级sheet（如果有涨停股票）
        if limit_up_stocks:
            print(f"\n🏆 开始创建晋级sheet...")

            # 使用核心日期处理逻辑
            current_date = parse_sheet_date(sheet_name)  # 解析当日日期
            if current_date:
                next_trading_date = get_next_trading_date(current_date)  # 计算明日
                new_sheet_name = next_trading_date.strftime('%m%d')  # 后四位，如0725
                print(f"📅 晋级sheet命名为下一交易日: {next_trading_date.strftime('%Y-%m-%d')} → {new_sheet_name}")
            else:
                # 如果无法解析日期，使用原来的命名方式
                new_sheet_name = f"{sheet_name}-{promotion_suffix}"
                print(f"⚠️ 无法解析日期，使用默认命名: {new_sheet_name}")
                next_trading_date = None

            # 如果目标sheet已存在，先删除
            if new_sheet_name in workbook.sheetnames:
                workbook.remove(workbook[new_sheet_name])
                print(f"🗑️ 删除已存在的 {new_sheet_name} sheet")

            # 复制sheet
            new_sheet = workbook.copy_worksheet(worksheet)
            new_sheet.title = new_sheet_name

            # 将新sheet移动到原sheet的左侧
            try:
                original_index = workbook.sheetnames.index(sheet_name)
                workbook.move_sheet(new_sheet, offset=-(len(workbook.sheetnames) - original_index))
                print(f"📍 晋级sheet已移动到 {sheet_name} 的左侧")
            except Exception as e:
                print(f"⚠️ 移动sheet位置失败: {str(e)}，但sheet已创建")

            # 只删除非涨停的股票简称，保留其他所有内容和原始格式
            cells_to_clear = []

            for row in new_sheet.iter_rows():
                for cell in row:
                    if cell.value:
                        cell_value = str(cell.value).strip()

                        # 检查是否为股票简称
                        if cell_value in all_stocks and cell_value not in limit_up_stocks:
                            # 非涨停股票，删除内容
                            cells_to_clear.append(cell)

            # 清除非涨停股票的单元格内容
            for cell in cells_to_clear:
                cell.value = None

            # 删除空行
            print("🗑️ 正在删除晋级sheet中的空行...")
            rows_to_delete = []

            # 从后往前检查，避免删除行时索引变化的问题
            for row_idx in range(new_sheet.max_row, 0, -1):
                row_has_content = False
                for col_idx in range(1, new_sheet.max_column + 1):
                    cell_value = new_sheet.cell(row=row_idx, column=col_idx).value
                    if cell_value is not None and str(cell_value).strip():
                        row_has_content = True
                        break

                if not row_has_content:
                    rows_to_delete.append(row_idx)

            # 删除空行
            for row_idx in rows_to_delete:
                new_sheet.delete_rows(row_idx)

            if rows_to_delete:
                print(f"✅ 删除了 {len(rows_to_delete)} 个空行")

            # 重置所有单元格格式为默认并添加细框线
            print("🔄 正在重置晋级sheet格式为默认并添加细框线...")
            default_font = Font()  # 默认字体

            # 定义细边框样式
            thin_border = Border(
                left=Side(style="thin", color="000000"),
                right=Side(style="thin", color="000000"),
                top=Side(style="thin", color="000000"),
                bottom=Side(style="thin", color="000000")
            )

            # 获取实际有数据的范围
            max_row = new_sheet.max_row
            max_col = new_sheet.max_column

            # 为所有有数据的单元格设置格式
            for row_idx in range(1, max_row + 1):
                for col_idx in range(1, max_col + 1):
                    cell = new_sheet.cell(row=row_idx, column=col_idx)
                    # 重置字体为默认
                    cell.font = default_font
                    # 清除填充色
                    cell.fill = PatternFill()
                    # 添加细边框
                    cell.border = thin_border

            # 清除工作表的条件格式
            try:
                if hasattr(new_sheet, 'conditional_formatting') and hasattr(new_sheet.conditional_formatting, '_cf_rules'):
                    new_sheet.conditional_formatting._cf_rules = {}
                    print("✅ 成功清除条件格式")
            except Exception:
                try:
                    from openpyxl.formatting.formatting import ConditionalFormattingList
                    new_sheet.conditional_formatting = ConditionalFormattingList()
                    print("✅ 成功重置条件格式")
                except Exception:
                    print("⚠️ 无法清除条件格式，但字体和填充色已重置")

            # 添加首板股票数据到最右侧（参考daily_limit_precisequery.py）
            if next_trading_date:
                # 计算晋级sheet对应的日期（下一个交易日）
                # next_trading_date 已经在上面计算过了
                print(f"� 调试信息:")
                # 删除包含未定义变量的调试信息
                print(f"�📊 正在获取 {next_trading_date.strftime('%Y-%m-%d')} 首板股票数据并添加到最右侧...")
                first_board_df = get_first_board_stocks_data(next_trading_date)

                if first_board_df is not None and not first_board_df.empty:
                    # 找到最右侧的列
                    current_max_col = new_sheet.max_column
                    start_col = current_max_col + 2  # 留一列空隙

                    # 添加标题行
                    title_row = 1
                    headers = ['股票简称', '涨停原因类别', '几天几板', '首次涨停', '最终涨停', '换手率', '流通市值']

                    for i, header in enumerate(headers):
                        cell = new_sheet.cell(row=title_row, column=start_col + i, value=header)
                        cell.font = Font(bold=True)
                        cell.border = thin_border

                    # 添加数据
                    for row_idx, (_, row) in enumerate(first_board_df.iterrows(), 2):
                        for col_idx, header in enumerate(headers):
                            value = row.get(header, '') if header in first_board_df.columns else ''
                            cell = new_sheet.cell(row=row_idx, column=start_col + col_idx, value=value)
                            cell.border = thin_border

                    # 应用条件格式（参考daily_limit_precisequery.py）
                    apply_conditional_formatting_to_first_board(new_sheet, first_board_df, headers, start_col, title_row)

                    print(f"✅ 已添加 {len(first_board_df)} 只首板股票数据到最右侧")
                else:
                    print("⚠️ 未获取到首板股票数据")

            print(f"✅ 成功创建晋级sheet: {new_sheet_name}")
            print(f"📊 保留了 {len(limit_up_stocks)} 只涨停股票")
            print(f"🗑️ 删除了 {len(cells_to_clear)} 个非涨停股票单元格")
            if rows_to_delete:
                print(f"🗑️ 删除了 {len(rows_to_delete)} 个空行")
            print("📐 已为所有单元格添加细框线")
            print("✨ 已重置所有格式为默认（字体默认、无填充色、无条件格式）")
        else:
            print("⚠️ 没有涨停股票，跳过晋级sheet创建")

        # 保存文件
        workbook.save(output_file_path)

        # 验证文件保存
        if os.path.exists(output_file_path):
            file_size = os.path.getsize(output_file_path)
            print(f"✅ 文件保存成功，大小: {file_size} 字节")

            # 返回操作结果
            result = {
                'annotated_count': annotated_count,
                'missing_count': len(missing_stocks) if missing_stocks else 0,
                'limit_up_count': len(limit_up_stocks),
                'promotion_sheet_created': len(limit_up_stocks) > 0,
                'promotion_sheet_name': f"{sheet_name}-{promotion_suffix}" if limit_up_stocks else None
            }
            return True, result
        else:
            print("❌ 文件保存失败")
            return False, None

    except Exception as e:
        print(f"❌ Excel标注和晋级sheet创建失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False, None



# 删除未使用的create_promotion_sheet函数 - 开始删除
# 删除未使用的create_promotion_sheet函数 - 函数体已删除

# ==================== 文件处理函数 ====================

def get_excel_files(folder_path):
    """获取文件夹中的所有Excel文件"""
    excel_files = []
    if os.path.exists(folder_path):
        for file in os.listdir(folder_path):
            if file.endswith('.xlsx') or file.endswith('.xls'):
                excel_files.append(file)
    return excel_files

def get_excel_sheets(file_path):
    """获取Excel文件中的所有sheet名称"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {str(e)}")
        return []

def parse_sheet_date(sheet_name):
    """解析工作表名称中的日期"""
    import re

    # 尝试匹配各种日期格式
    patterns = [
        r'(\d{4})(\d{2})(\d{2})',  # YYYYMMDD
        r'(\d{2})(\d{2})(\d{2})',  # YYMMDD
        r'(\d{1,2})(\d{2})',       # MMDD
        r'(\d{4})\.(\d{2})\.(\d{2})',  # YYYY.MM.DD
        r'(\d{4})-(\d{2})-(\d{2})',    # YYYY-MM-DD
    ]

    for pattern in patterns:
        match = re.search(pattern, str(sheet_name))
        if match:
            groups = match.groups()
            try:
                if len(groups) == 2:  # MMDD格式
                    month, day = int(groups[0]), int(groups[1])
                    # 假设是当前年份
                    year = datetime.now().year
                    return datetime(year, month, day).date()
                elif len(groups) == 3:
                    if len(groups[0]) == 2:  # YYMMDD格式
                        year = 2000 + int(groups[0])
                        month, day = int(groups[1]), int(groups[2])
                    else:  # YYYYMMDD格式
                        year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
                    return datetime(year, month, day).date()
            except (ValueError, TypeError):
                continue

    return None

def find_latest_sheet(file_path):
    """智能找到最新的工作表（通过日期命名）"""
    try:
        sheets = get_excel_sheets(file_path)
        if not sheets:
            return None

        print(f"\n📋 检测到 {len(sheets)} 个工作表:")

        # 解析所有工作表的日期
        sheet_dates = []
        for sheet in sheets:
            parsed_date = parse_sheet_date(sheet)
            if parsed_date:
                sheet_dates.append((sheet, parsed_date))
                print(f"   📅 {sheet} → {parsed_date.strftime('%Y-%m-%d')}")
            else:
                print(f"   📄 {sheet} → 无法解析日期")

        if not sheet_dates:
            print("⚠️ 未找到包含日期的工作表，使用第一个工作表")
            return sheets[0]

        # 找到最新日期的工作表
        latest_sheet, latest_date = max(sheet_dates, key=lambda x: x[1])
        print(f"\n✅ 自动选择最新工作表: {latest_sheet} ({latest_date.strftime('%Y-%m-%d')})")

        return latest_sheet

    except Exception as e:
        print(f"❌ 查找最新工作表失败: {str(e)}")
        return None

def select_excel_file_and_auto_sheet():
    """选择Excel文件并自动选择最新的工作表"""
    # 默认路径和文件
    default_path = r"C:\Users\<USER>\Nutstore\1\我的坚果云"
    default_filename = "复盘.xlsx"

    print("\n📁 Excel文件选择")
    print("=" * 50)

    # 1. 路径选择
    print("📁 路径选择:")
    print("1. 使用默认路径")
    print("2. 自定义路径")

    path_choice = input("请选择路径方式 (1/2，直接回车默认选择1): ").strip()
    if not path_choice:
        path_choice = '1'

    while path_choice not in ['1', '2']:
        path_choice = input("❌ 请输入1或2: ").strip()
        if not path_choice:
            path_choice = '1'

    if path_choice == '1':
        folder_path = default_path
        print(f"✅ 使用默认路径: {folder_path}")
    else:
        folder_path = input("请输入Excel文件所在文件夹路径: ").strip()
        print(f"✅ 使用路径: {folder_path}")

    # 检查路径是否存在
    if not os.path.exists(folder_path):
        print(f"❌ 路径不存在: {folder_path}")
        return None, None, None

    # 2. 文件选择
    print("\n� 文件选择:")
    print("1. 使用默认文件 (复盘.xlsx)")
    print("2. 自定义选择文件")

    file_choice = input("请选择文件方式 (1/2，直接回车默认选择1): ").strip()
    if not file_choice:
        file_choice = '1'  # 默认选择复盘.xlsx

    while file_choice not in ['1', '2']:
        file_choice = input("❌ 请输入1或2: ").strip()
        if not file_choice:
            file_choice = '1'

    if file_choice == '1':
        # 使用默认文件
        file_path = os.path.join(folder_path, default_filename)

        # 检查默认文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 默认文件不存在: {file_path}")
            print("请选择'自定义选择文件'或确认文件路径")
            return None, None, None

        print(f"✅ 使用默认文件: {default_filename}")

    else:
        # 自定义选择文件
        # 获取Excel文件列表
        excel_files = get_excel_files(folder_path)
        if not excel_files:
            print(f"❌ 在 {folder_path} 中未找到Excel文件")
            return None, None, None

        # 选择Excel文件
        print(f"\n📋 找到 {len(excel_files)} 个Excel文件:")
        for i, file in enumerate(excel_files, 1):
            print(f"   {i}. {file}")

        while True:
            try:
                choice = int(input(f"请选择Excel文件 (1-{len(excel_files)}): ").strip())
                if 1 <= choice <= len(excel_files):
                    selected_file = excel_files[choice - 1]
                    break
                else:
                    print(f"❌ 请输入1到{len(excel_files)}之间的数字")
            except ValueError:
                print("❌ 请输入有效数字")

        file_path = os.path.join(folder_path, selected_file)
        print(f"✅ 选择文件: {selected_file}")

    # 自动找到最新的工作表
    latest_sheet = find_latest_sheet(file_path)
    if not latest_sheet:
        print("❌ 无法找到合适的工作表")
        return None, None, None

    return file_path, latest_sheet, os.path.dirname(file_path)



def main():
    """主函数 - 智能涨停晋级分析"""
    try:
        print("🚀 涨停晋级分析工具")
        print("🤖 自动识别sheet日期，智能标注并创建晋级文件")

        # 选择Excel文件并自动选择最新工作表
        file_path, selected_sheet, _ = select_excel_file_and_auto_sheet()

        if not file_path or not selected_sheet:
            print("❌ 未选择有效的Excel文件")
            return

        # 解析sheet日期
        current_date = parse_sheet_date(selected_sheet)
        if not current_date:
            print("❌ 无法解析工作表日期")
            return

        print(f"📅 {current_date.strftime('%m%d')} → ", end="")

        # 计算次日日期
        next_trading_date = get_next_trading_date(current_date)

        # 获取数据并处理
        performance_df, continue_limit_up_names, limit_down_names, current_limit_up_dict = get_optimized_performance_data(current_date, next_trading_date)

        if performance_df is not None:
                result_df = process_optimized_performance_data(performance_df, continue_limit_up_names, limit_down_names)

                if result_df is not None:
                    print("\n📊 次日表现统计:")
                    print("=" * 50)

                    # 显示统计信息
                    stats = result_df['次日状态'].value_counts()
                    total_count = len(result_df)

                    print(f"📈 总计: {total_count} 只股票")

                    # 统计涨停、上涨、下跌数量
                    limit_up_count = len([s for s in stats.index if s == '涨停'])
                    up_count = len([s for s in stats.index if s.startswith('+')])
                    down_count = len([s for s in stats.index if s.startswith('-')])

                    if limit_up_count > 0:
                        print(f"🔥 涨停: {limit_up_count} 只 ({limit_up_count/total_count*100:.1f}%)")
                    if up_count > 0:
                        print(f"📈 上涨: {up_count} 只 ({up_count/total_count*100:.1f}%)")
                    if down_count > 0:
                        print(f"📉 下跌: {down_count} 只 ({down_count/total_count*100:.1f}%)")

                    # 显示前20个结果
                    print(f"\n📋 表现详情（前20个）:")
                    display_df = result_df.drop('标注样式', axis=1) if '标注样式' in result_df.columns else result_df
                    for i, (_, row) in enumerate(display_df.head(20).iterrows(), 1):
                        print(f"   {i:2d}. {row['股票简称']:8s} | {row['次日状态']:>8s}")

                    if len(result_df) > 20:
                        print(f"   ... 还有 {len(result_df) - 20} 只股票")

                    # 自动标注原文件并创建晋级sheet
                    print("\n" + "=" * 50)
                    print("🚀 开始自动标注原Excel文件并创建晋级sheet...")

                    # 自动使用默认的晋级后缀
                    promotion_suffix = "晋级"

                    # 直接标注原文件并创建晋级sheet
                    success, result = annotate_and_create_promotion_excel(
                        file_path, selected_sheet, result_df, file_path, promotion_suffix, current_limit_up_dict
                    )
                    if success:
                        print("🎉 原文件标注和晋级sheet创建完成！")
                        if result:
                            print(f"📊 标注了 {result['annotated_count']} 只股票")
                            if result['promotion_sheet_created']:
                                print(f"🏆 创建了晋级sheet: {result['promotion_sheet_name']}")
                            if result['missing_count'] > 0:
                                print(f"➕ 添加了 {result['missing_count']} 只缺失股票")
                    else:
                        print("❌ 标注失败")
                else:
                    print("❌ 处理表现数据失败")
        else:
            print("❌ 获取涨停股票数据失败")

    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()

