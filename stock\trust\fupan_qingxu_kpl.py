import streamlit as st
import pandas as pd
import requests
import json
from datetime import datetime,timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots


# API 配置
BASE_URL = "https://apphis.longhuvip.com/w1/api/index.php"

HEADERS = {
    "Host": "apphis.longhuvip.com",
    "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
    "User-Agent": "lhb/5.17.9 (com.kaipanla.www; build:0; iOS 16.6.0) Alamofire/4.9.1",
    "Accept": "*/*",
    "Accept-Language": "zh-Hans-CN;q=1.0",
    "Accept-Encoding": "gzip;q=1.0, compress;q=0.5",
    "Connection": "keep-alive"
}

FORM_DATA = {
    "index": 0,
    "PhoneOSNew": 2,
    "VerSion": "********",
    "View": "2,4,5,7,10",
    "a": "ChangeStatistics",
    "apiv": "w38",
    "c": "HisHomeDingPan",
    "st": 1000
}


def fetch_data():
    """从 API 获取情绪数据"""
    try:
        response = requests.post(
            BASE_URL,
            headers=HEADERS,
            data=FORM_DATA,
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        st.error(f"数据获取失败: {str(e)}")
        return None
    except json.JSONDecodeError:
        st.error("API 返回了无效的 JSON 数据")
        return None


def process_data(raw_data):
    """处理原始数据并转换为 DataFrame"""
    if not raw_data or "info" not in raw_data:
        st.warning("API 未返回有效数据")
        return None, ""

    df = pd.DataFrame(raw_data["info"])

    # 转换日期格式
    df['Day'] = pd.to_datetime(df['Day'])

    # 转换数据类型
    numeric_cols = ['strong', 'ztjs', 'lbgd', 'df_num']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric)

    # 按日期排序
    df = df.sort_values('Day', ascending=True)

    return df, raw_data.get("tip", "")


# 主应用
def app():
    st.title("📈 股票市场情绪分析仪表板")


    with st.spinner("正在获取最新数据..."):
        raw_data = fetch_data()
        if raw_data:
            df, tip = process_data(raw_data)
            if df is not None:
                st.session_state.df = df
                st.session_state.tip = tip
                st.session_state.last_update = datetime.now()

    if "df" not in st.session_state:
        st.warning("点击上方按钮获取数据")
        return

    # 显示最后更新时间
    if "last_update" in st.session_state:
        st.caption(f"最后更新时间: {st.session_state.last_update.strftime('%Y-%m-%d %H:%M:%S')}")

    # 显示提示信息
    if st.session_state.tip:
        with st.expander("📌 市场提示"):
            st.info(st.session_state.tip)

    # 日期范围选择器
    #min_date = st.session_state.df['Day'].min().date()
    max_date = st.session_state.df['Day'].max().date()

    min_date = max_date - timedelta(days=14)

    col1, col2 = st.columns(2)
    with col1:
        start_date = st.date_input(
            "开始日期",
            value=min_date,
            min_value=min_date,
            max_value=max_date
        )
    with col2:
        end_date = st.date_input(
            "结束日期",
            value=max_date,
            min_value=min_date,
            max_value=max_date
        )

    # 过滤数据
    filtered_df = st.session_state.df[
        (st.session_state.df['Day'].dt.date >= start_date) &
        (st.session_state.df['Day'].dt.date <= end_date)
        ]

    # 创建2x2的子图布局（4个独立折线图）
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=("情绪温度趋势", "涨停家数趋势", "连板高度趋势", "亏钱效应趋势"),
        vertical_spacing=0.15,
        horizontal_spacing=0.1
    )

    # 1. 情绪温度折线图（左上）
    fig.add_trace(
        go.Scatter(
            x=filtered_df['Day'],
            y=filtered_df['strong'],
            name='情绪温度',
            mode='lines+markers',
            line=dict(color='#636EFA', width=2),
            marker=dict(size=6)
        ),
        row=1, col=1
    )
    # 添加警戒线
    fig.add_hline(y=75, line_dash="dot", line_color="red", row=1, col=1,
                  annotation_text="过热警戒线", annotation_position="top right")
    fig.add_hline(y=25, line_dash="dot", line_color="green", row=1, col=1,
                  annotation_text="过冷警戒线", annotation_position="bottom right")
    fig.update_yaxes(title_text="情绪指数(0-100)", range=[0, 100], row=1, col=1)

    # 2. 涨停家数折线图（右上）
    fig.add_trace(
        go.Scatter(
            x=filtered_df['Day'],
            y=filtered_df['ztjs'],
            name='涨停家数',
            mode='lines+markers',
            line=dict(color='#00C853', width=2),
            marker=dict(size=6, symbol='diamond')
        ),
        row=1, col=2
    )
    # 添加活跃警戒线
    fig.add_hline(y=50, line_dash="dot", line_color="orange", row=1, col=2,
                  annotation_text="情绪活跃线", annotation_position="top right")
    fig.update_yaxes(title_text="涨停数量", row=1, col=2)

    # 3. 连板高度折线图（左下）
    fig.add_trace(
        go.Scatter(
            x=filtered_df['Day'],
            y=filtered_df['lbgd'],
            name='连板高度',
            mode='lines+markers',
            line=dict(color='#FF6D00', width=2, dash='dot'),
            marker=dict(size=7, symbol='triangle-up')
        ),
        row=2, col=1
    )
    # 添加龙头股识别线
    fig.add_hline(y=5, line_dash="dot", line_color="purple", row=2, col=1,
                  annotation_text="龙头股阈值", annotation_position="top right")
    fig.update_yaxes(title_text="连板天数", row=2, col=1)

    # 4. 亏钱效应折线图（右下）
    fig.add_trace(
        go.Scatter(
            x=filtered_df['Day'],
            y=filtered_df['df_num'],
            name='亏钱效应',
            mode='lines+markers',
            line=dict(color='#D50000', width=2),
            marker=dict(size=6, symbol='x')
        ),
        row=2, col=2
    )
    # 添加风险警戒线
    fig.add_hline(y=30, line_dash="dot", line_color="brown", row=2, col=2,
                  annotation_text="风险警戒线", annotation_position="top right")
    fig.update_yaxes(title_text="跌停数量", row=2, col=2)

    # 统一设置布局
    fig.update_layout(
        height=700,
        showlegend=False,  # 每个图表独立展示，无需图例
        template='plotly_white',
        margin=dict(t=50, b=50),
        hovermode='x unified'
    )
    fig.update_xaxes(title_text="日期", row=2, col=1)
    fig.update_xaxes(title_text="日期", row=2, col=2)

    st.plotly_chart(fig, use_container_width=True)

    # 显示原始数据
    st.subheader("📊 详细数据")
    st.dataframe(filtered_df.sort_values('Day', ascending=False).reset_index(drop=True))

    # 添加下载按钮
    csv = filtered_df.to_csv(index=False).encode('utf-8')
    st.download_button(
        label="下载CSV数据",
        data=csv,
        file_name='stock_sentiment_data.csv',
        mime='text/csv'
    )


if __name__ == "__main__":
    st.set_page_config(
        page_title="股票市场情绪分析仪表板",
        layout="wide",
        page_icon="📈"
    )
    app()