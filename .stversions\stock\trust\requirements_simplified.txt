# 简化的requirements.txt - 适用于pip安装
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
akshare==1.16.74
altair==5.5.0
APScheduler==3.11.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
beautifulsoup4>=4.11.0
blinker==1.9.0
cachetools==6.1.0
certifi>=2022.12.7
cffi>=1.15.0
charset-normalizer>=3.0.0
click==8.1.8
colorama>=0.4.0
comm==0.2.2
contourpy>=1.0.0
cycler>=0.11.0
DateTime==5.5
debugpy==1.8.14
decorator==5.2.1
et_xmlfile==2.0.0
exceptiongroup==1.3.0
executing==2.2.0
fake-useragent==1.5.1
fonttools>=4.38.0
frozenlist==1.5.0
gitdb==4.0.12
GitPython==3.1.44
h2>=4.0.0
hpack>=4.0.0
html5lib>=1.1
hyperframe>=6.0.0
idna>=3.4
importlib_metadata==8.7.0
ipykernel==6.29.5
ipython==8.18.1
jedi==0.19.2
Jinja2==3.1.6
jsonpath==0.82.2
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.8.1
kiwisolver>=1.4.0
lxml==5.3.2
MarkupSafe==3.0.2
matplotlib==3.9.2
matplotlib-inline==0.1.7
mini-racer==0.12.4
multidict==6.3.2
narwhals==1.46.0
nest-asyncio==1.6.0
numpy>=1.20.0
openpyxl==3.1.5
packaging>=21.0
pandas>=1.5.0
parso==0.8.4
pillow>=9.0.0
platformdirs==4.3.8
plotly==6.2.0
ply==3.11
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==6.31.1
psutil==7.0.0
pure_eval==0.2.3
py-mini-racer==0.6.0
pyarrow==20.0.0
pycparser>=2.21
pydash==7.0.7
pydeck==0.9.1
PyExecJS==1.5.1
Pygments==2.19.2
pyparsing>=3.0.0
PySocks>=1.7.0
python-dateutil>=2.8.0
pytz>=2022.1
pywencai==0.13.1
pyzmq==27.0.0
referencing==0.36.2
requests>=2.28.0
rpds-py==0.26.0
six>=1.16.0
smmap==5.0.2
soupsieve>=2.3.0
stack-data==0.6.3
streamlit==1.46.1
tabulate==0.9.0
tenacity==9.1.2
toml==0.10.2
tomli>=2.0.0
tornado>=6.2
tqdm>=4.64.0
traitlets==5.14.3
typing_extensions>=4.4.0
tzdata>=2022.1
tzlocal==5.3.1
urllib3>=1.26.0
watchdog==6.0.0
wcwidth==0.2.13
webencodings>=0.5.1
xlrd==2.0.1
yarl==1.19.0
zipp>=3.8.0
zope.interface==7.2
zstandard==0.23.0
