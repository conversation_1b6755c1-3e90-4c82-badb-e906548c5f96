#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易日处理功能
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta, date

def test_trade_date_functions():
    """测试交易日相关函数"""
    print("="*60)
    print("测试交易日处理功能")
    print("="*60)
    
    def get_trade_dates():
        """获取交易日历数据"""
        try:
            trade_dates_df = ak.tool_trade_date_hist_sina()
            trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
            return trade_dates_df
        except Exception as e:
            print(f"获取交易日历数据失败: {str(e)}")
            return pd.DataFrame()

    def get_next_trade_date(selected_date):
        """获取指定日期的下一个交易日"""
        try:
            trade_dates_df = get_trade_dates()
            if trade_dates_df.empty:
                print("无法获取交易日历，使用简单日期计算")
                return selected_date + timedelta(days=1)
            
            # 将选择的日期转换为datetime
            if isinstance(selected_date, date):
                selected_datetime = datetime.combine(selected_date, datetime.min.time())
            else:
                selected_datetime = selected_date
            
            # 查找下一个交易日
            future_dates = trade_dates_df[trade_dates_df['trade_date'] > selected_datetime]
            
            if not future_dates.empty:
                next_trade_date = future_dates.iloc[0]['trade_date']
                return next_trade_date.date()
            else:
                print("未找到下一个交易日，使用简单日期计算")
                return selected_date + timedelta(days=1)
                
        except Exception as e:
            print(f"计算下一个交易日失败: {str(e)}")
            return selected_date + timedelta(days=1)

    def is_trade_date(check_date):
        """检查指定日期是否为交易日"""
        try:
            trade_dates_df = get_trade_dates()
            if trade_dates_df.empty:
                return True  # 如果无法获取数据，默认认为是交易日
            
            # 将检查日期转换为datetime
            if isinstance(check_date, date):
                check_datetime = datetime.combine(check_date, datetime.min.time())
            else:
                check_datetime = check_date
            
            # 检查是否在交易日列表中
            return not trade_dates_df[trade_dates_df['trade_date'].dt.date == check_datetime.date()].empty
            
        except Exception as e:
            print(f"检查交易日失败: {str(e)}")
            return True
    
    # 测试获取交易日历
    print("1. 测试获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    if not trade_dates_df.empty:
        print(f"✅ 成功获取交易日历，共 {len(trade_dates_df)} 个交易日")
        print(f"最新交易日: {trade_dates_df['trade_date'].max().date()}")
        print(f"最早交易日: {trade_dates_df['trade_date'].min().date()}")
    else:
        print("❌ 获取交易日历失败")
    
    # 测试日期场景
    test_dates = [
        date(2025, 7, 21),  # 周一
        date(2025, 7, 22),  # 周二
        date(2025, 7, 25),  # 周五
        date(2025, 7, 26),  # 周六
        date(2025, 7, 27),  # 周日
    ]
    
    print(f"\n2. 测试交易日检查和下一交易日计算...")
    for test_date in test_dates:
        is_trade = is_trade_date(test_date)
        next_trade = get_next_trade_date(test_date)
        weekday = test_date.strftime('%A')
        
        print(f"日期: {test_date} ({weekday})")
        print(f"  是否交易日: {'✅' if is_trade else '❌'}")
        print(f"  下一交易日: {next_trade}")
        print(f"  间隔天数: {(next_trade - test_date).days}")
        print()

def test_weekend_handling():
    """测试周末处理逻辑"""
    print("="*60)
    print("测试周末处理逻辑")
    print("="*60)
    
    # 模拟不同的当前日期
    test_scenarios = [
        ("周一选择昨日", date(2025, 7, 21)),  # 周一，昨日应该是周五
        ("周二选择昨日", date(2025, 7, 22)),  # 周二，昨日应该是周一
        ("周六选择昨日", date(2025, 7, 26)),  # 周六，昨日应该是周五
        ("周日选择昨日", date(2025, 7, 27)),  # 周日，昨日应该是周五
    ]
    
    for scenario_name, current_date in test_scenarios:
        print(f"\n场景: {scenario_name} (当前: {current_date})")
        
        # 模拟默认昨日选择逻辑
        default_yesterday = current_date - timedelta(days=1)
        
        # 如果是周一，默认选择上周五
        if default_yesterday.weekday() == 6:  # 周日
            default_yesterday = default_yesterday - timedelta(days=2)
        elif default_yesterday.weekday() == 5:  # 周六
            default_yesterday = default_yesterday - timedelta(days=1)
        
        print(f"  默认昨日: {default_yesterday} ({default_yesterday.strftime('%A')})")
        
        # 计算下一个交易日
        try:
            from datetime import datetime
            import akshare as ak
            
            # 简化的下一交易日计算
            next_date = default_yesterday + timedelta(days=1)
            while next_date.weekday() >= 5:  # 跳过周末
                next_date += timedelta(days=1)
            
            print(f"  计算今日: {next_date} ({next_date.strftime('%A')})")
            print(f"  间隔天数: {(next_date - default_yesterday).days}")
            
        except Exception as e:
            print(f"  计算失败: {str(e)}")

def test_query_format():
    """测试查询语句格式"""
    print("="*60)
    print("测试查询语句格式")
    print("="*60)
    
    # 测试日期
    yesterday = date(2025, 7, 21)
    today = date(2025, 7, 22)
    
    yesterday_str = yesterday.strftime('%Y%m%d')
    today_str = today.strftime('%Y%m%d')
    
    print(f"昨日: {yesterday} -> {yesterday_str}")
    print(f"今日: {today} -> {today_str}")
    
    # 生成查询语句
    queries = {
        "昨日涨停": f"沪深主板,非ST,{yesterday_str}涨停",
        "次日涨跌幅": f"{yesterday_str}涨停,沪深主板,非ST,下一个交易日（即{today_str}）涨跌幅",
        "次日涨停": f"{yesterday_str}涨停,沪深主板,非ST,下一个交易日（即{today_str}）涨停",
        "次日跌停": f"{yesterday_str}涨停,沪深主板,非ST,下一个交易日（即{today_str}）跌停"
    }
    
    print(f"\n查询语句示例:")
    for name, query in queries.items():
        print(f"  {name}: {query}")

if __name__ == "__main__":
    print("测试交易日处理功能")
    print("="*60)
    
    test_trade_date_functions()
    test_weekend_handling()
    test_query_format()
    
    print("\n" + "="*60)
    print("测试完成!")
    print("="*60)
