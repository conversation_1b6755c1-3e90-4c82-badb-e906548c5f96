import requests
import pandas as pd
from bs4 import BeautifulSoup

# 目标网址
url = 'https://www.tgb.cn/a/2eEufNvgTqA'

# 发送请求并获取网页内容
response = requests.get(url)

# 确保请求成功
if response.status_code == 200:
    # 解析网页内容
    soup = BeautifulSoup(response.text, 'html.parser')
    
    # 提取数据
    data = {
        '标题': [soup.find('h1', class_='article-title').get_text().strip() if soup.find('h1', class_='article-title') else ''],
        '正文': [soup.find('div', class_='article-content').get_text().strip() if soup.find('div', class_='article-content') else ''],
        '发布时间': [soup.find('span', class_='time').get_text().strip() if soup.find('span', class_='time') else '']
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存到Excel
    df.to_excel('article_data.xlsx', index=False)
    
    # 打印网页正文
    print("网页正文内容：")
    print(data['正文'][0])
    
    # 简单的分类统计
    word_count = len(data['正文'][0].split())
    print(f"文章已保存到article_data.xlsx\n字数统计: {word_count}")
else:
    print(f"请求失败，状态码：{response.status_code}")

