#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版批量获取2025年6月至7月18日涨停分析数据
"""

import pandas as pd
import akshare as ak
import pywencai
from datetime import datetime, date, timedelta
import time
import os

def get_trade_dates():
    """获取交易日历数据"""
    try:
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        print(f"获取交易日历数据时出错: {str(e)}")
        return pd.DataFrame()

def get_market_data(date, query_type):
    """获取市场数据"""
    query_map = {
        'limit_up': f"非ST,沪深主板,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,沪深主板,{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,沪深主板,{date.strftime('%Y%m%d')}曾涨停",
        'yesterday_limit_up': f"非ST,沪深主板,{date.strftime('%Y%m%d')}涨跌幅"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        return df if df is not None and not df.empty else None
    except Exception as e:
        print(f"  获取{query_type}数据时出错: {str(e)}")
        return None

def calculate_basic_stats(selected_date, selected_df, previous_df, poban_df, selected_limit_down_df):
    """计算基础统计数据"""
    date_str = selected_date.strftime("%Y%m%d")
    
    # 基础数据统计
    total_limit_up = len(selected_df) if selected_df is not None and not selected_df.empty else 0
    total_limit_down = len(selected_limit_down_df) if selected_limit_down_df is not None and not selected_limit_down_df.empty else 0
    total_poban = len(poban_df) if poban_df is not None and not poban_df.empty else 0

    # 计算首板数
    first_board_count = 0
    max_board = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            selected_df_copy = selected_df.copy()
            selected_df_copy[days_col] = pd.to_numeric(selected_df_copy[days_col], errors='coerce')
            first_board_count = len(selected_df_copy[selected_df_copy[days_col] == 1])
            max_board = selected_df_copy[days_col].max()
            if pd.isna(max_board):
                max_board = 0

    # 计算破板率
    total_attempt = total_limit_up + total_poban
    poban_rate = round(total_poban / total_attempt * 100, 2) if total_attempt > 0 else 0

    # 计算昨日涨停今日上涨率
    up_count, total_count, yesterday_up_rate = calculate_yesterday_performance(selected_date, previous_df)

    return {
        '日期': selected_date.strftime('%Y-%m-%d'),
        '涨停家数': total_limit_up,
        '跌停家数': total_limit_down,
        '首板家数': first_board_count,
        '最高板数': int(max_board) if max_board > 0 else 0,
        '破板家数': total_poban,
        '破板率': f"{poban_rate}%",
        '昨日涨停今日上涨': f"{up_count}/{total_count}",
        '昨日涨停今日上涨率': f"{yesterday_up_rate}%"
    }

def calculate_yesterday_performance(selected_date, previous_df):
    """计算昨日涨停今日表现"""
    if previous_df is None or previous_df.empty:
        return 0, 0, 0.0

    try:
        yesterday_codes = set(previous_df['股票代码'].tolist())
        today_market_df = get_market_data(selected_date, 'yesterday_limit_up')

        if today_market_df is None or today_market_df.empty:
            return 0, 0, 0.0

        yesterday_zt_today_df = today_market_df[
            today_market_df['股票代码'].isin(yesterday_codes)
        ].copy()

        if yesterday_zt_today_df.empty:
            return 0, 0, 0.0

        # 找涨跌幅列
        zhangdiefu_col = None
        for col in yesterday_zt_today_df.columns:
            if '涨跌幅' in col or '涨幅' in col:
                zhangdiefu_col = col
                break

        if zhangdiefu_col is None:
            return 0, 0, 0.0

        yesterday_zt_today_df[zhangdiefu_col] = pd.to_numeric(
            yesterday_zt_today_df[zhangdiefu_col], errors='coerce'
        )

        valid_data = yesterday_zt_today_df.dropna(subset=[zhangdiefu_col])

        if valid_data.empty:
            return 0, 0, 0.0

        up_count = len(valid_data[valid_data[zhangdiefu_col] > 0])
        total_count = len(valid_data)
        up_rate = round(up_count / total_count * 100, 2) if total_count > 0 else 0

        return up_count, total_count, up_rate

    except Exception as e:
        print(f"  计算昨日涨停今日表现时出错: {str(e)}")
        return 0, 0, 0.0

def get_trading_days_in_range(start_date, end_date):
    """获取指定日期范围内的所有交易日"""
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日历数据")
        return []
    
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    
    # 筛选指定范围内的交易日
    range_trading_days = [
        d for d in trading_dates 
        if start_date <= d <= end_date
    ]
    
    return sorted(range_trading_days)

def analyze_single_date(analysis_date, trade_dates_df):
    """分析单个交易日的数据"""
    print(f"正在分析 {analysis_date.strftime('%Y-%m-%d')}...")
    
    # 获取前一交易日
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < analysis_date]
    if not previous_dates:
        print(f"  {analysis_date} 没有前一交易日数据，跳过")
        return None
    
    previous_date = max(previous_dates)
    
    try:
        # 获取各类数据
        selected_df = get_market_data(analysis_date, 'limit_up')
        previous_df = get_market_data(previous_date, 'limit_up')
        poban_df = get_market_data(analysis_date, 'poban')
        selected_limit_down_df = get_market_data(analysis_date, 'limit_down')
        
        # 计算统计数据
        stats = calculate_basic_stats(
            analysis_date, selected_df, previous_df, poban_df, selected_limit_down_df
        )
        
        print(f"  ✅ {analysis_date} 分析完成 - 涨停:{stats['涨停家数']}家")
        return stats
        
    except Exception as e:
        print(f"  ❌ {analysis_date} 分析失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🚀 批量获取2025年6月至7月18日涨停数据")
    print("=" * 60)
    
    # 设置日期范围
    start_date = date(2025, 6, 1)
    end_date = date(2025, 7, 18)
    
    print(f"📅 分析日期范围: {start_date} 至 {end_date}")
    
    # 获取交易日历
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日历数据")
        return
    
    # 获取指定范围内的交易日
    trading_days = get_trading_days_in_range(start_date, end_date)
    if not trading_days:
        print("指定日期范围内没有交易日")
        return
    
    print(f"找到 {len(trading_days)} 个交易日")
    print()
    
    # 存储所有分析结果
    all_results = []
    successful_count = 0
    
    # 逐个分析每个交易日
    for i, trading_day in enumerate(trading_days, 1):
        print(f"[{i}/{len(trading_days)}] ", end="")
        
        result = analyze_single_date(trading_day, trade_dates_df)
        if result is not None:
            all_results.append(result)
            successful_count += 1
        
        # 添加延迟避免请求过于频繁
        if i < len(trading_days):
            time.sleep(3)
    
    print()
    print(f"分析完成: 成功 {successful_count}/{len(trading_days)} 个交易日")
    
    # 导出到Excel
    if all_results:
        output_file = f"涨停数据_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"
        
        # 创建DataFrame
        df = pd.DataFrame(all_results)
        
        # 导出到Excel
        try:
            df.to_excel(output_file, index=False, sheet_name='涨停数据汇总')
            print(f"✅ 数据已成功导出到 {output_file}")
            print(f"📊 共包含 {len(all_results)} 个交易日的数据")
            
            # 显示数据概览
            print("\n📈 数据概览:")
            print(f"   涨停家数范围: {df['涨停家数'].min()} - {df['涨停家数'].max()}")
            print(f"   平均涨停家数: {df['涨停家数'].mean():.1f}")
            
        except Exception as e:
            print(f"❌ 导出Excel文件时出错: {str(e)}")
    else:
        print("没有成功分析的数据，无法生成Excel文件")

if __name__ == "__main__":
    main()
