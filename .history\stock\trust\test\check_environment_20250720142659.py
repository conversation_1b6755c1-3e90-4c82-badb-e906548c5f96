#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查工具
检查Python环境和依赖库是否正确安装
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查:")
    print(f"   版本: {sys.version}")
    print(f"   路径: {sys.executable}")
    
    version_info = sys.version_info
    if version_info.major >= 3 and version_info.minor >= 7:
        print("   ✅ Python版本符合要求 (>=3.7)")
    else:
        print("   ❌ Python版本过低，建议升级到3.7+")
    print()

def check_required_packages():
    """检查必需的包"""
    print("📦 依赖包检查:")
    
    required_packages = {
        'pandas': 'pandas',
        'pywencai': 'pywencai', 
        'akshare': 'akshare',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package_name, import_name in required_packages.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', '未知版本')
            print(f"   ✅ {package_name}: {version}")
        except ImportError:
            print(f"   ❌ {package_name}: 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下包: {', '.join(missing_packages)}")
        print("📥 安装命令:")
        for package in missing_packages:
            print(f"   pip install {package}")
    else:
        print("\n✅ 所有必需包都已安装")
    
    return len(missing_packages) == 0

def test_pywencai():
    """测试pywencai是否能正常工作"""
    print("\n🔧 pywencai功能测试:")
    
    try:
        import pywencai
        print("   ✅ pywencai导入成功")
        
        # 尝试简单查询
        print("   🔍 尝试简单查询...")
        df = pywencai.get(query="平安银行", loop=False)
        
        if df is not None and not df.empty:
            print(f"   ✅ 查询成功，获取到 {len(df)} 条数据")
            print(f"   📊 数据列: {list(df.columns)[:5]}...")  # 显示前5列
        else:
            print("   ⚠️  查询返回空数据，可能是网络问题")
            
    except ImportError:
        print("   ❌ pywencai未安装")
        return False
    except Exception as e:
        print(f"   ❌ pywencai测试失败: {str(e)}")
        return False
    
    return True

def test_file_operations():
    """测试文件操作权限"""
    print("\n📁 文件操作测试:")
    
    try:
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"   📂 当前目录: {current_dir}")
        
        # 测试写入权限
        test_file = os.path.join(current_dir, "test_write_permission.txt")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件写入权限")
        
        print("   ✅ 文件写入权限正常")
        
        # 清理测试文件
        os.remove(test_file)
        print("   ✅ 文件删除权限正常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件操作失败: {str(e)}")
        return False

def test_csv_creation():
    """测试CSV文件创建"""
    print("\n📊 CSV创建测试:")
    
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = {
            '列1': ['数据1', '数据2', '数据3'],
            '列2': [1, 2, 3],
            '列3': [1.1, 2.2, 3.3]
        }
        
        df = pd.DataFrame(test_data)
        
        # 保存CSV
        current_dir = os.path.dirname(os.path.abspath(__file__))
        csv_file = os.path.join(current_dir, "test_csv_creation.csv")
        
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        # 检查文件是否存在
        if os.path.exists(csv_file):
            file_size = os.path.getsize(csv_file)
            print(f"   ✅ CSV文件创建成功: {csv_file}")
            print(f"   📏 文件大小: {file_size} 字节")
            
            # 清理测试文件
            os.remove(csv_file)
            return True
        else:
            print("   ❌ CSV文件创建失败")
            return False
            
    except Exception as e:
        print(f"   ❌ CSV创建测试失败: {str(e)}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n🔧 常见问题解决方案:")
    print("=" * 50)
    
    print("1. 如果缺少依赖包:")
    print("   pip install pywencai pandas akshare openpyxl")
    print()
    
    print("2. 如果pywencai安装失败:")
    print("   pip install pywencai -i https://pypi.tuna.tsinghua.edu.cn/simple/")
    print()
    
    print("3. 如果网络连接问题:")
    print("   - 检查网络连接")
    print("   - 尝试使用VPN")
    print("   - 检查防火墙设置")
    print()
    
    print("4. 如果权限问题:")
    print("   - 以管理员身份运行命令行")
    print("   - 检查文件夹写入权限")
    print()
    
    print("5. 如果仍有问题:")
    print("   - 重启Python环境")
    print("   - 重新安装相关包")
    print("   - 检查Python环境变量")

def main():
    """主检查函数"""
    print("🔍 Python环境诊断工具")
    print("=" * 60)
    
    # 检查Python版本
    check_python_version()
    
    # 检查依赖包
    packages_ok = check_required_packages()
    
    if not packages_ok:
        print("\n❌ 请先安装缺少的依赖包")
        provide_solutions()
        return
    
    # 测试pywencai
    pywencai_ok = test_pywencai()
    
    # 测试文件操作
    file_ok = test_file_operations()
    
    # 测试CSV创建
    csv_ok = test_csv_creation()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    print("=" * 60)
    print(f"Python版本: ✅")
    print(f"依赖包: {'✅' if packages_ok else '❌'}")
    print(f"pywencai: {'✅' if pywencai_ok else '❌'}")
    print(f"文件操作: {'✅' if file_ok else '❌'}")
    print(f"CSV创建: {'✅' if csv_ok else '❌'}")
    
    if all([packages_ok, pywencai_ok, file_ok, csv_ok]):
        print("\n🎉 环境检查通过！可以正常运行问财查询脚本")
    else:
        print("\n⚠️  环境存在问题，请根据上述提示解决")
        provide_solutions()

if __name__ == "__main__":
    main()
