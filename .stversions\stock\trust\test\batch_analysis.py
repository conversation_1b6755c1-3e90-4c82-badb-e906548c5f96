#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量获取指定日期范围的涨停分析数据
"""

import sys
import os
import pandas as pd
from datetime import datetime, date, timedelta
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接复制需要的函数（避免导入问题）
def get_trade_dates():
    """获取交易日历数据"""
    try:
        import akshare as ak
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def get_market_data(date, query_type):
    """获取市场数据"""
    import pywencai
    query_map = {
        'limit_up': f"非ST,沪深主板,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,沪深主板,{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,沪深主板,{date.strftime('%Y%m%d')}曾涨停",
        'yesterday_limit_up': f"非ST,沪深主板,{date.strftime('%Y%m%d')}涨跌幅"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        return df if df is not None and not df.empty else None
    except Exception as e:
        print(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_concept_counts(df, date, min_count=2):
    """统计涨停概念"""
    if df is None or df.empty:
        return pd.DataFrame()

    reason_col = f'涨停原因类别[{date.strftime("%Y%m%d")}]'
    if reason_col not in df.columns:
        possible_cols = [col for col in df.columns if '涨停原因' in col or '概念' in col]
        if possible_cols:
            reason_col = possible_cols[0]
        else:
            return pd.DataFrame()

    try:
        concepts_series = df[reason_col].dropna().astype(str)
        concepts_series = concepts_series[~concepts_series.isin(['nan', 'None', '', '无'])]

        if concepts_series.empty:
            return pd.DataFrame()

        concepts = concepts_series.str.split('+').explode().reset_index(drop=True)
        concepts = concepts.str.strip()
        concepts = concepts[concepts != '']

        if concepts.empty:
            return pd.DataFrame()

        concept_counts = concepts.value_counts()
        concept_counts = concept_counts[concept_counts >= min_count]

        if concept_counts.empty:
            return pd.DataFrame()

        concept_stats = [
            {'指标': '热门概念', '数值': f"{concept}({count})"}
            for concept, count in concept_counts.head(10).items()
        ]

        return pd.DataFrame(concept_stats)

    except Exception as e:
        print(f"统计概念时出错: {str(e)}")
        return pd.DataFrame()

def get_yesterday_limit_up_performance(selected_date, previous_df):
    """计算昨日涨停股票今日表现"""
    if previous_df is None or previous_df.empty:
        return 0, 0, 0.0

    try:
        yesterday_codes = set(previous_df['股票代码'].tolist())
        today_market_df = get_market_data(selected_date, 'yesterday_limit_up')

        if today_market_df is None or today_market_df.empty:
            return 0, 0, 0.0

        yesterday_zt_today_df = today_market_df[
            today_market_df['股票代码'].isin(yesterday_codes)
        ].copy()

        if yesterday_zt_today_df.empty:
            return 0, 0, 0.0

        zhangdiefu_col = None
        for col in yesterday_zt_today_df.columns:
            if '涨跌幅' in col or '涨幅' in col:
                zhangdiefu_col = col
                break

        if zhangdiefu_col is None:
            return 0, 0, 0.0

        yesterday_zt_today_df[zhangdiefu_col] = pd.to_numeric(
            yesterday_zt_today_df[zhangdiefu_col], errors='coerce'
        )

        valid_data = yesterday_zt_today_df.dropna(subset=[zhangdiefu_col])

        if valid_data.empty:
            return 0, 0, 0.0

        up_count = len(valid_data[valid_data[zhangdiefu_col] > 0])
        total_count = len(valid_data)
        up_rate = round(up_count / total_count * 100, 2) if total_count > 0 else 0

        return up_count, total_count, up_rate

    except Exception as e:
        print(f"计算昨日涨停今日表现时出错: {str(e)}")
        return 0, 0, 0.0

def calculate_market_stats(selected_date, selected_df, previous_df, poban_df, selected_limit_down_df):
    """计算市场统计数据"""
    date_str = selected_date.strftime("%Y%m%d")
    stats = []

    # 基础数据统计
    total_limit_up = len(selected_df) if selected_df is not None and not selected_df.empty else 0
    total_limit_down = len(selected_limit_down_df) if selected_limit_down_df is not None and not selected_limit_down_df.empty else 0
    total_poban = len(poban_df) if poban_df is not None and not poban_df.empty else 0

    # 计算首板数
    first_board_count = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            selected_df_copy = selected_df.copy()
            selected_df_copy[days_col] = pd.to_numeric(selected_df_copy[days_col], errors='coerce')
            first_board_count = len(selected_df_copy[selected_df_copy[days_col] == 1])

    # 计算最高板
    max_board = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            selected_df_copy = selected_df.copy()
            selected_df_copy[days_col] = pd.to_numeric(selected_df_copy[days_col], errors='coerce')
            max_board = selected_df_copy[days_col].max()
            if pd.isna(max_board):
                max_board = 0

    # 计算破板率
    total_attempt = total_limit_up + total_poban
    poban_rate = round(total_poban / total_attempt * 100, 2) if total_attempt > 0 else 0

    # 计算昨日涨停今日上涨率
    up_count, total_count, yesterday_up_rate = get_yesterday_limit_up_performance(selected_date, previous_df)

    stats.extend([
        {'指标': '日期', '数值': selected_date.strftime('%Y-%m-%d')},
        {'指标': '涨停家数', '数值': total_limit_up},
        {'指标': '跌停家数', '数值': total_limit_down},
        {'指标': '首板家数', '数值': first_board_count},
        {'指标': '最高板数', '数值': int(max_board) if max_board > 0 else 0},
        {'指标': '破板家数', '数值': total_poban},
        {'指标': '破板率', '数值': f"{poban_rate}%"},
        {'指标': '昨日涨停今日上涨', '数值': f"{up_count}/{total_count}"},
        {'指标': '昨日涨停今日上涨率', '数值': f"{yesterday_up_rate}%"}
    ])

    return pd.DataFrame(stats)

def calculate_promotion_rates_detailed(current_df, previous_df, current_date, previous_date):
    """计算详细的连板晋级率"""
    if current_df is None or current_df.empty or previous_df is None or previous_df.empty:
        return pd.DataFrame()

    current_days_col = f'连续涨停天数[{current_date.strftime("%Y%m%d")}]'
    previous_days_col = f'连续涨停天数[{previous_date.strftime("%Y%m%d")}]'

    stats = []

    # 处理数据
    current_df_copy = current_df.copy()
    if current_days_col in current_df_copy.columns:
        current_df_copy[current_days_col] = pd.to_numeric(current_df_copy[current_days_col], errors='coerce')
        current_df_copy[current_days_col] = current_df_copy[current_days_col].fillna(1)
    else:
        current_df_copy[current_days_col] = 1

    previous_df_copy = previous_df.copy()
    if previous_days_col in previous_df_copy.columns:
        previous_df_copy[previous_days_col] = pd.to_numeric(previous_df_copy[previous_days_col], errors='coerce')
        previous_df_copy[previous_days_col] = previous_df_copy[previous_days_col].fillna(1)
    else:
        previous_df_copy[previous_days_col] = 1

    # 统计各板块数量
    for i in range(1, 7):
        count = len(current_df_copy[current_df_copy[current_days_col] == i])
        stats.append({'指标': f'{i}板数量', '数值': count})

    count_6plus = len(current_df_copy[current_df_copy[current_days_col] >= 7])
    stats.append({'指标': '6板以上数量', '数值': count_6plus})

    # 计算晋级率
    total_prev = len(previous_df_copy)
    total_curr_promoted = len(current_df_copy[current_df_copy[current_days_col] > 1])
    total_rate = round(total_curr_promoted / total_prev * 100, 2) if total_prev > 0 else 0
    stats.append({'指标': '总晋级率', '数值': f"{total_rate}%"})

    for i in range(1, 7):
        prev_count = len(previous_df_copy[previous_df_copy[previous_days_col] == i])
        curr_count = len(current_df_copy[current_df_copy[current_days_col] == i + 1])
        rate = round(curr_count / prev_count * 100, 2) if prev_count > 0 else 0
        stats.append({'指标': f'{i}进{i+1}晋级率', '数值': f"{rate}% ({curr_count}/{prev_count})"})

    prev_count_6plus = len(previous_df_copy[previous_df_copy[previous_days_col] >= 7])
    curr_count_7plus = len(current_df_copy[current_df_copy[current_days_col] >= 8])
    rate_6plus = round(curr_count_7plus / prev_count_6plus * 100, 2) if prev_count_6plus > 0 else 0
    stats.append({'指标': '6板以上晋级率', '数值': f"{rate_6plus}% ({curr_count_7plus}/{prev_count_6plus})"})

    return pd.DataFrame(stats)

def get_trading_days_in_range(start_date, end_date):
    """获取指定日期范围内的所有交易日"""
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日历数据")
        return []
    
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    
    # 筛选指定范围内的交易日
    range_trading_days = [
        d for d in trading_dates 
        if start_date <= d <= end_date
    ]
    
    return sorted(range_trading_days)

def analyze_single_date(analysis_date, trade_dates_df):
    """分析单个交易日的数据"""
    print(f"正在分析 {analysis_date.strftime('%Y-%m-%d')}...")
    
    # 获取前一交易日
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < analysis_date]
    if not previous_dates:
        print(f"  {analysis_date} 没有前一交易日数据，跳过")
        return None
    
    previous_date = max(previous_dates)
    
    try:
        # 获取各类数据
        selected_df = get_market_data(analysis_date, 'limit_up')
        previous_df = get_market_data(previous_date, 'limit_up')
        poban_df = get_market_data(analysis_date, 'poban')
        selected_limit_down_df = get_market_data(analysis_date, 'limit_down')
        
        # 计算统计数据
        market_stats = calculate_market_stats(
            analysis_date, selected_df, previous_df, poban_df, selected_limit_down_df
        )
        
        promotion_rates = calculate_promotion_rates_detailed(
            selected_df, previous_df, analysis_date, previous_date
        )
        
        concept_counts = get_concept_counts(selected_df, analysis_date, min_count=2)
        
        # 合并所有统计数据
        all_stats = pd.concat([
            market_stats,
            promotion_rates,
            concept_counts
        ], ignore_index=True)
        
        print(f"  ✅ {analysis_date} 分析完成")
        return all_stats
        
    except Exception as e:
        print(f"  ❌ {analysis_date} 分析失败: {str(e)}")
        return None

def batch_analyze_dates(start_date, end_date, output_file):
    """批量分析指定日期范围的数据"""
    print(f"开始批量分析: {start_date} 至 {end_date}")
    print("=" * 60)
    
    # 获取交易日历
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日历数据")
        return
    
    # 获取指定范围内的交易日
    trading_days = get_trading_days_in_range(start_date, end_date)
    if not trading_days:
        print("指定日期范围内没有交易日")
        return
    
    print(f"找到 {len(trading_days)} 个交易日:")
    for day in trading_days:
        print(f"  {day}")
    print()
    
    # 存储所有分析结果
    all_results = {}
    successful_count = 0
    
    # 逐个分析每个交易日
    for i, trading_day in enumerate(trading_days, 1):
        print(f"[{i}/{len(trading_days)}] ", end="")
        
        result = analyze_single_date(trading_day, trade_dates_df)
        if result is not None:
            all_results[trading_day] = result
            successful_count += 1
        
        # 添加延迟避免请求过于频繁
        if i < len(trading_days):
            time.sleep(2)
    
    print()
    print(f"分析完成: 成功 {successful_count}/{len(trading_days)} 个交易日")
    
    # 导出到Excel
    if all_results:
        export_to_excel(all_results, output_file)
    else:
        print("没有成功分析的数据，无法生成Excel文件")

def export_to_excel(all_results, output_file):
    """将所有结果导出到Excel"""
    print(f"\n正在导出数据到 {output_file}...")
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建汇总表
            create_summary_sheet(all_results, writer)
            
            # 为每个日期创建详细表
            for trading_day, result_df in all_results.items():
                sheet_name = trading_day.strftime('%m%d')
                result_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ 数据已成功导出到 {output_file}")
        print(f"📁 包含 {len(all_results) + 1} 个工作表:")
        print(f"   - 汇总数据 (所有日期的关键指标)")
        for trading_day in sorted(all_results.keys()):
            print(f"   - {trading_day.strftime('%m%d')} ({trading_day})")
            
    except Exception as e:
        print(f"❌ 导出Excel文件时出错: {str(e)}")

def create_summary_sheet(all_results, writer):
    """创建汇总工作表"""
    summary_data = []
    
    # 定义要汇总的关键指标
    key_metrics = [
        '日期', '涨停家数', '跌停家数', '首板家数', '最高板数', 
        '破板家数', '破板率', '昨日涨停今日上涨率',
        '1板数量', '2板数量', '3板数量', '4板数量', '5板数量', '6板数量', '6板以上数量',
        '总晋级率', '1进2晋级率', '2进3晋级率', '3进4晋级率', '4进5晋级率', '5进6晋级率', '6进7晋级率'
    ]
    
    for trading_day in sorted(all_results.keys()):
        result_df = all_results[trading_day]
        
        # 提取关键指标
        row_data = {'交易日': trading_day.strftime('%Y-%m-%d')}
        
        for metric in key_metrics:
            matching_rows = result_df[result_df['指标'] == metric]
            if not matching_rows.empty:
                value = matching_rows.iloc[0]['数值']
                row_data[metric] = value
            else:
                row_data[metric] = ''
        
        summary_data.append(row_data)
    
    # 创建汇总DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 导出汇总表
    summary_df.to_excel(writer, sheet_name='汇总数据', index=False)

def main():
    """主函数"""
    print("🚀 批量涨停分析工具")
    print("=" * 60)
    
    # 设置日期范围
    start_date = date(2025, 6, 1)
    end_date = date(2025, 7, 18)
    
    # 生成输出文件名
    output_file = f"batch_analysis_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"
    
    print(f"📅 分析日期范围: {start_date} 至 {end_date}")
    print(f"📄 输出文件: {output_file}")
    print()
    
    # 确认是否继续
    confirm = input("是否开始批量分析? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("已取消分析")
        return
    
    # 开始批量分析
    batch_analyze_dates(start_date, end_date, output_file)
    
    print("\n🎉 批量分析完成!")

if __name__ == "__main__":
    main()
