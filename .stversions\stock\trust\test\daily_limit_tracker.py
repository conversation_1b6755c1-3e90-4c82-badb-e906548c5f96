import pywencai
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta, date
import openpyxl
from openpyxl.styles import Font, PatternFill
import streamlit as st
import os

@st.cache_data(ttl=3600)  # 缓存1小时
def get_trade_dates():
    """获取交易日历数据"""
    try:
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        st.error(f"获取交易日历数据失败: {str(e)}")
        return pd.DataFrame()

def get_next_trade_date(selected_date):
    """获取指定日期的下一个交易日"""
    try:
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            # 如果获取交易日历失败，使用简单的日期加1
            st.warning("无法获取交易日历，使用简单日期计算")
            return selected_date + timedelta(days=1)

        # 将选择的日期转换为datetime
        if isinstance(selected_date, date):
            selected_datetime = datetime.combine(selected_date, datetime.min.time())
        else:
            selected_datetime = selected_date

        # 查找下一个交易日
        future_dates = trade_dates_df[trade_dates_df['trade_date'] > selected_datetime]

        if not future_dates.empty:
            next_trade_date = future_dates.iloc[0]['trade_date']
            return next_trade_date.date()
        else:
            # 如果没有找到未来的交易日，使用简单加1天
            st.warning("未找到下一个交易日，使用简单日期计算")
            return selected_date + timedelta(days=1)

    except Exception as e:
        st.error(f"计算下一个交易日失败: {str(e)}")
        return selected_date + timedelta(days=1)

def is_trade_date(check_date):
    """检查指定日期是否为交易日"""
    try:
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            return True  # 如果无法获取数据，默认认为是交易日

        # 将检查日期转换为datetime
        if isinstance(check_date, date):
            check_datetime = datetime.combine(check_date, datetime.min.time())
        else:
            check_datetime = check_date

        # 检查是否在交易日列表中
        return not trade_dates_df[trade_dates_df['trade_date'].dt.date == check_datetime.date()].empty

    except Exception as e:
        st.warning(f"检查交易日失败: {str(e)}")
        return True





def extract_all_stock_names_from_excel(df):
    """从Excel的所有单元格中提取可能的股票简称"""
    stock_names = set()

    # 遍历所有单元格（跳过第一行第一列，因为通常是标题）
    for row_idx in range(len(df)):
        for col_idx in range(len(df.columns)):
            # 跳过第一行第一列
            if row_idx == 0 and col_idx == 0:
                continue

            cell_value = df.iloc[row_idx, col_idx]

            # 转换为字符串并清理
            if pd.notna(cell_value):
                cell_str = str(cell_value).strip()

                # 过滤掉明显不是股票名称的内容
                if (len(cell_str) >= 2 and len(cell_str) <= 10 and
                    cell_str != '' and cell_str != 'nan' and
                    not cell_str.isdigit() and  # 不是纯数字
                    not cell_str.replace('.', '').replace('%', '').replace('-', '').isdigit()):  # 不是数字格式

                    # 进一步过滤：排除常见的非股票名称
                    exclude_keywords = ['合计', '小计', '总计', '备注', '说明', '分类', '类别',
                                      '日期', '时间', '序号', '编号', '行业', '板块']

                    if not any(keyword in cell_str for keyword in exclude_keywords):
                        stock_names.add(cell_str)

    # 转换为列表并排序
    stock_names_list = sorted(list(stock_names))

    return stock_names_list











def get_optimized_performance_data(yesterday_date, today_date):
    """优化的获取昨日涨停股票次日表现逻辑"""
    try:
        yesterday_str = yesterday_date.strftime('%Y%m%d')
        today_str = today_date.strftime('%Y%m%d')

        st.info(f"正在获取{yesterday_str}涨停股票在{today_str}的表现...")

        # 步骤1: 获取今日继续涨停的股票
        st.info("步骤1: 获取昨日涨停今日继续涨停的股票")
        continue_limit_up_names = set()
        try:
            continue_query = f"{yesterday_str}涨停,沪深主板,非ST,{today_str}涨停"
            continue_limit_up_df = pywencai.get(query=continue_query, loop=True)
            if continue_limit_up_df is not None and not continue_limit_up_df.empty:
                continue_limit_up_names = set(continue_limit_up_df['股票简称'].tolist())
                st.success(f"获取到{len(continue_limit_up_names)}只今日继续涨停股票")
        except Exception as e:
            st.warning(f"获取继续涨停数据失败: {str(e)}")

        # 步骤2: 获取今日跌停的股票
        st.info("步骤2: 获取昨日涨停今日跌停的股票")
        limit_down_names = set()
        try:
            limit_down_query = f"{yesterday_str}涨停,沪深主板,非ST,{today_str}跌停"
            limit_down_df = pywencai.get(query=limit_down_query, loop=True)
            if limit_down_df is not None and not limit_down_df.empty:
                limit_down_names = set(limit_down_df['股票简称'].tolist())
                st.success(f"获取到{len(limit_down_names)}只今日跌停股票")
        except Exception as e:
            st.warning(f"获取跌停数据失败: {str(e)}")

        # 步骤3: 获取所有昨日涨停股票的今日涨跌幅
        st.info("步骤3: 获取昨日涨停股票的今日涨跌幅")
        performance_query = f"{yesterday_str}涨停,沪深主板,非ST,{today_str}涨跌幅"
        performance_df = pywencai.get(query=performance_query, loop=True)

        if performance_df is None or performance_df.empty:
            st.error("获取涨跌幅数据失败")
            return None, None, None

        st.success(f"获取到{len(performance_df)}只股票的涨跌幅数据")

        # 显示数据预览
        with st.expander("数据预览"):
            st.write("**涨跌幅数据列名:**", list(performance_df.columns))
            st.dataframe(performance_df.head())

            # 显示股票简称列的内容
            if '股票简称' in performance_df.columns:
                sample_names = performance_df['股票简称'].head(10).tolist()
                st.write("**股票简称样本:**", sample_names)

            # 显示涨跌幅列的内容（支持动态日期格式）
            change_keywords = ['涨跌幅', '涨跌', '涨跌率']
            found_change_cols = []
            for col in performance_df.columns:
                col_str = str(col)
                if any(keyword in col_str for keyword in change_keywords):
                    found_change_cols.append(col)

            if found_change_cols:
                st.write(f"**找到涨跌幅列:** {found_change_cols}")
                for col in found_change_cols[:1]:  # 只显示第一个找到的列
                    sample_changes = performance_df[col].head(10).tolist()
                    st.write(f"**{col}样本:**", sample_changes)
            else:
                st.warning("未找到涨跌幅相关列")
                st.write("**所有列名:**", list(performance_df.columns))

        return performance_df, continue_limit_up_names, limit_down_names

    except Exception as e:
        st.error(f"获取表现数据失败: {str(e)}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")
        return None, set(), set()



def get_gradient_color(change_pct, is_limit_up=False, is_limit_down=False):
    """根据涨跌幅获取美化渐变颜色，每1%精细渐变"""

    # 涨停跌停特殊处理
    if is_limit_up:
        return "limit_up"  # 涨停：红色加粗字体
    elif is_limit_down:
        return "limit_down"  # 跌停：绿色加粗字体

    # 限制范围在-10%到10%之间
    change_pct = max(-10, min(10, change_pct))

    # 使用更美观的颜色方案：橙红色系 vs 青蓝色系
    if change_pct >= 0:
        # 上涨：从浅橙到深橙红 (0% -> 10%)
        # 使用HSL色彩空间实现更自然的渐变
        ratio = change_pct / 10.0  # 0到1

        # 橙红色系：从 #FFF5F5 (浅粉橙) 到 #FF4500 (橙红)
        # 基础色：橙红 (255, 69, 0)
        red = 255
        green = int(245 - (245 - 69) * ratio)  # 245 -> 69
        blue = int(245 - 245 * ratio)          # 245 -> 0

    else:
        # 下跌：从浅青到深青蓝 (0% -> -10%)
        ratio = abs(change_pct) / 10.0  # 0到1

        # 青蓝色系：从 #F0F8FF (浅青) 到 #008B8B (深青)
        # 基础色：深青 (0, 139, 139)
        red = int(240 - 240 * ratio)           # 240 -> 0
        green = int(248 - (248 - 139) * ratio) # 248 -> 139
        blue = int(255 - (255 - 139) * ratio)  # 255 -> 139

    # 转换为16进制颜色
    color_hex = f"{red:02X}{green:02X}{blue:02X}"
    return f"gradient_{color_hex}"

def apply_cell_style(cell, style_code, limit_up_font, limit_down_font):
    """统一的样式应用函数"""
    if style_code == "limit_up":
        cell.font = limit_up_font
    elif style_code == "limit_down":
        cell.font = limit_down_font
    elif style_code.startswith("gradient_"):
        color_hex = style_code.replace("gradient_", "")
        cell.fill = PatternFill(start_color=color_hex, end_color=color_hex, fill_type="solid")

def process_optimized_performance_data(performance_df, continue_limit_up_names, limit_down_names):
    """处理优化后的表现数据，使用条件格式"""
    try:
        result = []
        total_stocks = len(performance_df)

        # 创建进度条
        progress_bar = st.progress(0)
        status_text = st.empty()

        for idx, (_, row) in enumerate(performance_df.iterrows()):
            stock_name = row['股票简称']

            # 更新进度
            progress = (idx + 1) / total_stocks
            progress_bar.progress(progress)
            status_text.text(f"正在处理股票 {idx + 1}/{total_stocks}: {stock_name}")

            # 获取涨跌幅（支持动态日期格式）
            today_change = 0
            found_change_col = None

            # 先尝试精确匹配
            exact_cols = ['涨跌幅', '涨跌幅%', '涨跌幅(%)', '涨跌', '涨跌率']
            for col in exact_cols:
                if col in row.index:
                    try:
                        change_value = row[col]
                        if isinstance(change_value, str):
                            today_change = float(change_value.replace('%', ''))
                        else:
                            today_change = float(change_value)
                        found_change_col = col
                        break
                    except (ValueError, TypeError):
                        continue

            # 如果精确匹配失败，尝试模糊匹配（支持日期格式）
            if found_change_col is None:
                change_keywords = ['涨跌幅', '涨跌', '涨跌率']
                for col in row.index:
                    col_str = str(col)
                    if any(keyword in col_str for keyword in change_keywords):
                        try:
                            change_value = row[col]
                            if isinstance(change_value, str):
                                today_change = float(change_value.replace('%', ''))
                            else:
                                today_change = float(change_value)
                            found_change_col = col
                            break
                        except (ValueError, TypeError):
                            continue



            # 判断是否涨停或跌停（直接从查询结果判断）
            is_limit_up = stock_name in continue_limit_up_names
            is_limit_down = stock_name in limit_down_names

            # 获取渐变颜色
            style_code = get_gradient_color(today_change, is_limit_up, is_limit_down)



            # 确定状态描述
            if is_limit_up:
                status = "涨停"
            elif is_limit_down:
                status = "跌停"
            else:
                # 直接显示涨跌幅，不分类
                if today_change > 0:
                    status = f"+{today_change:.2f}%"
                elif today_change < 0:
                    status = f"{today_change:.2f}%"
                else:
                    status = "0.00%"

            # 收集调试信息，稍后在折叠框中显示
            debug_info = {
                'stock_name': stock_name,
                'change': today_change,
                'status': status,
                'style': style_code
            }
            if not hasattr(process_optimized_performance_data, 'debug_list'):
                process_optimized_performance_data.debug_list = []
            process_optimized_performance_data.debug_list.append(debug_info)

            result.append({
                '股票简称': stock_name,
                '昨日状态': '涨停',
                '次日涨跌幅': f"{today_change:.2f}%",
                '次日状态': status,
                '标注样式': style_code
            })

        # 清理进度条
        progress_bar.empty()
        status_text.empty()

        result_df = pd.DataFrame(result)

        # 显示处理完成信息
        if not result_df.empty:
            style_counts = result_df['标注样式'].value_counts()
            st.success("✅ 所有股票处理完成！")

            limit_up_count = len([s for s in style_counts.index if s == "limit_up"])
            limit_down_count = len([s for s in style_counts.index if s == "limit_down"])
            gradient_count = len([s for s in style_counts.index if s.startswith("gradient_")])

            # 简化显示，只显示关键信息
            if limit_up_count > 0:
                st.success(f"🔥 发现 {limit_up_count} 只涨停股票")
            if limit_down_count > 0:
                st.info(f"📉 发现 {limit_down_count} 只跌停股票")
            st.info(f"📊 共处理 {gradient_count} 只普通股票")

            # 在折叠框中显示详细调试信息
            if hasattr(process_optimized_performance_data, 'debug_list'):
                with st.expander("📋 查看详细处理信息", expanded=False):
                    for info in process_optimized_performance_data.debug_list:
                        if info['style'].startswith("gradient_"):
                            color_hex = info['style'].replace("gradient_", "")
                            # 使用HTML显示颜色预览
                            st.markdown(f"""
                            <div style="display: flex; align-items: center; margin: 2px 0;">
                                <div style="width: 20px; height: 20px; background-color: #{color_hex}; border: 1px solid #ccc; margin-right: 10px;"></div>
                                <span>股票 {info['stock_name']}: 涨跌幅={info['change']:.2f}%, 状态='{info['status']}', 颜色=#{color_hex}</span>
                            </div>
                            """, unsafe_allow_html=True)
                        else:
                            st.text(f"股票 {info['stock_name']}: 涨跌幅={info['change']:.2f}%, 状态='{info['status']}', 样式={info['style']}")
                # 清理调试信息
                delattr(process_optimized_performance_data, 'debug_list')

            # 添加颜色方案预览
            with st.expander("🎨 查看颜色方案预览", expanded=False):
                def create_color_preview(direction, label):
                    colors = []
                    for i in range(0, 11):
                        pct = i if direction == "up" else -i
                        style = get_gradient_color(pct, False, False)
                        if style.startswith("gradient_"):
                            color_hex = style.replace("gradient_", "")
                            sign = "+" if direction == "up" else "-"
                            colors.append(f'<div style="display: inline-block; width: 30px; height: 30px; background-color: #{color_hex}; border: 1px solid #ccc; margin: 2px; text-align: center; line-height: 30px; font-size: 10px;">{sign}{i}%</div>')
                    st.markdown(f"**{label}：**")
                    st.markdown(''.join(colors), unsafe_allow_html=True)

                create_color_preview("up", "上涨颜色渐变（橙红色系）")
                create_color_preview("down", "下跌颜色渐变（青蓝色系）")

        return result_df

    except Exception as e:
        st.error(f"处理表现数据失败: {str(e)}")
        return None

def annotate_excel_with_performance(original_file_path, sheet_name, performance_data, output_file_path):
    """根据获取的数据对原Excel文件进行条件格式标注"""
    try:
        # 检查输入参数
        if performance_data is None or performance_data.empty:
            st.error("表现数据为空，无法进行标注")
            return False

        if not os.path.exists(original_file_path):
            st.error(f"原始Excel文件不存在: {original_file_path}")
            return False

        # 读取原始Excel文件
        workbook = openpyxl.load_workbook(original_file_path)
        if sheet_name not in workbook.sheetnames:
            st.error(f"工作表 '{sheet_name}' 不存在")
            return False

        worksheet = workbook[sheet_name]

        # 创建股票名称到表现数据的映射
        performance_dict = {}
        for _, row in performance_data.iterrows():
            stock_name = row['股票简称']
            performance_dict[stock_name] = {
                '次日状态': row.get('次日状态', ''),
                '标注样式': row.get('标注样式', 'normal')
            }

        st.info(f"准备标注 {len(performance_dict)} 个股票")

        # 定义样式

        # 涨停跌停：美化字体样式
        limit_up_font = Font(
            name="Microsoft YaHei",  # 微软雅黑字体
            size=14,                 # 加大字体
            color="CC0000",          # 深红色
            bold=True,               # 加粗
            italic=False             # 不倾斜
        )

        limit_down_font = Font(
            name="Microsoft YaHei",  # 微软雅黑字体
            size=14,                 # 加大字体
            color="006600",          # 深绿色
            bold=True,               # 加粗
            italic=False             # 不倾斜
        )



        # 遍历所有单元格，查找股票名称并标注
        annotated_count = 0
        matched_stocks = []
        found_stocks = set()  # 记录在Excel中找到的股票
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value:
                    cell_value = str(cell.value).strip()

                    # 检查是否匹配股票名称
                    matched_performance = None
                    matched_stock_name = None

                    for stock_name, perf_data in performance_dict.items():
                        if cell_value == stock_name:  # 只精确匹配
                            matched_performance = perf_data
                            matched_stock_name = stock_name
                            found_stocks.add(stock_name)  # 记录找到的股票
                            break

                    if matched_performance:
                        style_code = matched_performance['标注样式']
                        status = matched_performance['次日状态']



                        # 应用样式
                        apply_cell_style(cell, style_code, limit_up_font, limit_down_font)

                        annotated_count += 1
                        matched_stocks.append({
                            'Excel中的名称': cell_value,
                            '匹配的股票': matched_stock_name,
                            '次日状态': status,
                            '位置': f"{cell.coordinate}"
                        })

        # 找出未在Excel中出现的昨日涨停股票
        missing_stocks = []
        for stock_name, perf_data in performance_dict.items():
            if stock_name not in found_stocks:
                missing_stocks.append({
                    '股票简称': stock_name,
                    '次日状态': perf_data['次日状态'],
                    '标注样式': perf_data['标注样式']
                })

        # 如果有未出现的股票，添加到Excel的最后一列
        if missing_stocks:
            # 找到有数据的最后一列
            max_col = 1
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        max_col = max(max_col, cell.column)

            # 在最后一列后面添加标题
            next_col = max_col + 1
            title_cell = worksheet.cell(row=1, column=next_col, value="未在表中的昨日涨停股票")
            title_cell.font = Font(bold=True)

            # 添加未出现的股票
            for i, stock_info in enumerate(missing_stocks, start=2):
                cell = worksheet.cell(row=i, column=next_col, value=stock_info['股票简称'])

                # 应用样式
                apply_cell_style(cell, stock_info['标注样式'], limit_up_font, limit_down_font)

            st.info(f"添加了 {len(missing_stocks)} 个未在Excel中出现的昨日涨停股票")

        # 保存文件
        try:
            workbook.save(output_file_path)

            # 验证文件是否真的被保存
            if os.path.exists(output_file_path):
                file_size = os.path.getsize(output_file_path)
                st.success(f"文件保存成功，大小: {file_size} 字节")
            else:
                st.error("文件保存失败，文件不存在")
                return False

        except Exception as save_error:
            st.error(f"保存文件时出错: {str(save_error)}")
            return False

        return True

    except Exception as e:
        st.error(f"Excel标注失败: {str(e)}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")
        return False

def create_promotion_sheet(original_file_path, sheet_name, performance_data, promotion_suffix="晋级"):
    """创建晋级sheet，保留原标注，只保留涨停股票简称"""
    try:
        # 获取涨停股票列表和所有股票列表
        limit_up_stocks = set()
        all_stocks = set()
        if performance_data is not None and not performance_data.empty:
            for _, row in performance_data.iterrows():
                stock_name = row['股票简称']
                all_stocks.add(stock_name)
                if row.get('标注样式') == 'limit_up':
                    limit_up_stocks.add(stock_name)

        if not limit_up_stocks:
            st.warning("没有涨停股票，无法创建晋级sheet")
            return False

        # 读取原始Excel文件
        workbook = openpyxl.load_workbook(original_file_path)

        # 复制原sheet
        source_sheet = workbook[sheet_name]
        new_sheet_name = f"{sheet_name}-{promotion_suffix}"

        # 如果目标sheet已存在，先删除
        if new_sheet_name in workbook.sheetnames:
            workbook.remove(workbook[new_sheet_name])

        # 复制sheet
        new_sheet = workbook.copy_worksheet(source_sheet)
        new_sheet.title = new_sheet_name

        # 只删除非涨停的股票简称，保留其他所有内容和原始格式
        cells_to_clear = []

        for row in new_sheet.iter_rows():
            for cell in row:
                if cell.value:
                    cell_value = str(cell.value).strip()

                    # 检查是否为股票简称
                    if cell_value in all_stocks and cell_value not in limit_up_stocks:
                        # 非涨停股票，删除内容
                        cells_to_clear.append(cell)

        # 清除非涨停股票的单元格内容
        for cell in cells_to_clear:
            cell.value = None

        # 保存文件
        workbook.save(original_file_path)

        st.success(f"✅ 成功创建晋级sheet: {new_sheet_name}")
        st.info(f"保留了 {len(limit_up_stocks)} 只涨停股票: {', '.join(sorted(limit_up_stocks))}")
        st.info(f"删除了 {len(cells_to_clear)} 个非涨停股票单元格")
        st.info("✨ 保留了所有原始格式")

        return True

    except Exception as e:
        st.error(f"创建晋级sheet失败: {str(e)}")
        import traceback
        st.error(f"详细错误: {traceback.format_exc()}")




def get_excel_files(folder_path):
    """获取文件夹中的所有Excel文件"""
    excel_files = []
    if os.path.exists(folder_path):
        for file in os.listdir(folder_path):
            if file.endswith('.xlsx') or file.endswith('.xls'):
                excel_files.append(file)
    return excel_files

def get_excel_sheets(file_path):
    """获取Excel文件中的所有sheet名称"""
    try:
        excel_file = pd.ExcelFile(file_path)
        return excel_file.sheet_names
    except Exception as e:
        st.error(f"读取Excel文件失败: {str(e)}")
        return []

def detect_data_range_and_read_excel(file_path, sheet_name):
    """智能检测Excel数据范围并读取有效数据（简化版本，直接提取股票名称）"""
    try:
        st.info("开始读取Excel文件...")

        # 直接读取Excel文件，不处理列名问题
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        st.info(f"读取成功，原始数据形状: {df.shape}")

        # 清理数据：去除完全空白的行和列
        df = df.dropna(how='all', axis=0)  # 删除全空行
        df = df.dropna(how='all', axis=1)  # 删除全空列
        st.info(f"清理空行空列后，形状: {df.shape}")

        # 提取所有可能的股票名称
        stock_names = extract_all_stock_names_from_excel(df)

        # 创建一个简单的DataFrame，只包含股票简称
        result_df = pd.DataFrame({'股票简称': stock_names})

        data_info = {
            'original_shape': df.shape,
            'final_shape': result_df.shape,
            'detected_range': "全表扫描提取股票名称",
            'columns': ['股票简称'],
            'stock_count': len(stock_names)
        }

        st.success(f"Excel数据读取和处理完成，提取到 {len(stock_names)} 个股票名称")
        return result_df, data_info

    except Exception as e:
        st.error(f"读取Excel文件失败: {str(e)}")
        import traceback
        st.error(f"详细错误信息: {traceback.format_exc()}")
        return None, None





def main():
    st.title("📊 涨停股票次日跟踪分析")

    # 日期选择界面
    st.header("📅 日期设置")

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("选择昨日日期")
        # 默认选择前一个工作日
        default_yesterday = datetime.now().date() - timedelta(days=1)

        # 如果是周一，默认选择上周五
        if default_yesterday.weekday() == 6:  # 周日
            default_yesterday = default_yesterday - timedelta(days=2)
        elif default_yesterday.weekday() == 5:  # 周六
            default_yesterday = default_yesterday - timedelta(days=1)

        yesterday = st.date_input(
            "昨日（涨停日期）",
            value=default_yesterday,
            help="选择要分析的涨停日期"
        )

        # 检查是否为交易日
        if is_trade_date(yesterday):
            st.success(f"✅ {yesterday} 是交易日")
        else:
            st.warning(f"⚠️ {yesterday} 不是交易日，请选择交易日")

    with col2:
        st.subheader("自动计算今日")
        # 自动计算下一个交易日
        today = get_next_trade_date(yesterday)

        st.info(f"📈 今日（次日）: {today}")
        st.caption("系统自动计算的下一个交易日")

        # 显示日期间隔
        date_diff = (today - yesterday).days
        if date_diff == 1:
            st.success("✅ 连续交易日")
        elif date_diff > 1:
            st.info(f"📅 间隔 {date_diff} 天（包含周末/节假日）")

    # 显示分析的日期范围
    st.markdown("---")
    st.info(f"🎯 **分析范围**: {yesterday} 涨停股票在 {today} 的表现")

    # 侧边栏显示日期信息
    st.sidebar.header("📊 分析日期")
    st.sidebar.write(f"**昨日（涨停日）**: {yesterday}")
    st.sidebar.write(f"**今日（次日）**: {today}")
    st.sidebar.write(f"**日期间隔**: {(today - yesterday).days} 天")
    
    # 简化的分析流程 - 直接进行Excel文件分析
    st.markdown("---")
    st.header("📁 Excel文件分析")
    st.info("选择包含股票名称的Excel文件，系统将自动获取这些股票的次日表现并进行标注")

    # 文件夹路径选择
    st.write("**选择Excel文件所在文件夹：**")

    # 默认路径选项
    default_path = r"C:\Users\<USER>\Nutstore\1\我的坚果云"

    # 路径选择方式
    path_option = st.radio(
        "选择路径方式：",
        ["使用默认路径", "自定义路径"],
        horizontal=True
    )

    if path_option == "使用默认路径":
        folder_path = default_path
        st.info(f"使用默认路径: {folder_path}")

        # 检查默认路径是否存在
        if not os.path.exists(folder_path):
            st.warning(f"默认路径不存在: {folder_path}")
            st.write("请选择'自定义路径'或确认默认路径是否正确")
            folder_path = ""
    else:
        folder_path = st.text_input(
            "输入Excel文件所在文件夹路径",
            value=default_path,
            help="可以修改上面的默认路径，或输入新的路径"
        )

        # 提供一些常用路径的快捷选择
        st.write("**常用路径快捷选择：**")
        common_paths = [
            ("桌面", os.path.join(os.path.expanduser("~"), "Desktop")),
            ("文档", os.path.join(os.path.expanduser("~"), "Documents")),
            ("下载", os.path.join(os.path.expanduser("~"), "Downloads")),
            ("坚果云", default_path)
        ]

        cols = st.columns(len(common_paths))
        for i, (name, path) in enumerate(common_paths):
            with cols[i]:
                if st.button(f"📁 {name}", key=f"path_{i}"):
                    folder_path = path
                    st.rerun()

    # 显示当前选择的路径状态
    if folder_path:
        if os.path.exists(folder_path):
            st.success(f"✅ 路径有效: {folder_path}")
        else:
            st.error(f"❌ 路径不存在: {folder_path}")

    if folder_path and os.path.exists(folder_path):
        # 获取文件夹中的Excel文件
        excel_files = get_excel_files(folder_path)

        if excel_files:
            # 选择Excel文件
            selected_file = st.selectbox("选择Excel文件", excel_files)
            file_path = os.path.join(folder_path, selected_file)

            # 获取并选择sheet
            sheets = get_excel_sheets(file_path)
            if sheets:
                selected_sheet = st.selectbox("选择工作表", sheets)

                if st.button("开始分析", type="primary"):
                    try:
                        # 智能读取Excel数据
                        with st.spinner("正在智能读取Excel数据..."):
                            excel_df, data_info = detect_data_range_and_read_excel(file_path, selected_sheet)

                        if excel_df is not None:
                            st.success(f"成功读取Excel文件，提取到 {data_info['stock_count']} 个股票名称")

                            # 显示数据预览
                            with st.expander("Excel数据预览"):
                                st.write("**提取的股票简称（前20个）：**")
                                stock_names = excel_df['股票简称'].tolist()
                                for i, name in enumerate(stock_names[:20], 1):
                                    st.write(f"{i}. {name}")
                                if len(stock_names) > 20:
                                    st.write(f"... 还有 {len(stock_names) - 20} 个")

                                st.write("**数据信息：**")
                                st.json(data_info)

                            # 直接获取昨日涨停股票的次日表现（不需要匹配）
                            with st.spinner("正在获取昨日涨停股票的次日表现..."):
                                performance_df, continue_limit_up_names, limit_down_names = get_optimized_performance_data(yesterday, today)

                            if performance_df is not None:
                                result_df = process_optimized_performance_data(performance_df, continue_limit_up_names, limit_down_names)

                                if result_df is not None:
                                    # 保存数据到session state
                                    st.session_state['result_df'] = result_df
                                    st.session_state['file_path'] = file_path
                                    st.session_state['selected_sheet'] = selected_sheet
                                    st.session_state['selected_file'] = selected_file
                                    st.session_state['folder_path'] = folder_path

                                    st.success("数据分析完成！")

                                    # 显示统计信息
                                    stats = result_df['次日状态'].value_counts()
                                    col1, col2, col3, col4 = st.columns(4)

                                    with col1:
                                        st.metric("总计", len(result_df))
                                    with col2:
                                        涨停数 = len([s for s in stats.index if s == '涨停'])
                                        st.metric("涨停", 涨停数)
                                    with col3:
                                        上涨数 = len([s for s in stats.index if s.startswith('+')])
                                        st.metric("上涨", 上涨数)
                                    with col4:
                                        下跌数 = len([s for s in stats.index if s.startswith('-')])
                                        st.metric("下跌", 下跌数)

                                    # 显示结果表格（不含标注样式列）
                                    display_df = result_df.drop('标注样式', axis=1) if '标注样式' in result_df.columns else result_df
                                    st.dataframe(display_df, use_container_width=True)


                                else:
                                    st.error("处理表现数据失败")
                            else:
                                st.error("获取昨日涨停股票数据失败")
                        else:
                            st.error("读取Excel文件失败")

                    except Exception as e:
                        st.error(f"处理失败: {str(e)}")
            else:
                st.error("无法读取Excel文件中的工作表")
        else:
            st.warning(f"在 {folder_path} 中未找到Excel文件")
    else:
        if folder_path:
            st.error(f"文件夹路径 {folder_path} 不存在")

    # 独立的Excel标注区域
    if 'result_df' in st.session_state and st.session_state['result_df'] is not None:
        st.markdown("---")
        st.header("📝 Excel文件标注")
        st.info("数据分析已完成，现在可以对原Excel文件进行标注")

        # 显示当前数据信息
        result_df = st.session_state['result_df']
        st.info(f"准备标注 {len(result_df)} 个股票的表现数据")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🎨 标注原Excel文件", type="primary", key="annotate_original"):
                file_path = st.session_state['file_path']
                selected_sheet = st.session_state['selected_sheet']

                with st.spinner("正在标注原Excel文件..."):
                    success = annotate_excel_with_performance(file_path, selected_sheet, result_df, file_path)

                if success:
                    st.balloons()
                    st.success("✅ 原文件标注完成！")
                else:
                    st.error("❌ 标注失败")

        with col2:
            if st.button("📄 创建标注副本", key="create_copy"):
                file_path = st.session_state['file_path']
                selected_sheet = st.session_state['selected_sheet']
                selected_file = st.session_state['selected_file']
                folder_path = st.session_state['folder_path']

                annotated_file = os.path.join(folder_path, f"标注_{selected_file}")

                with st.spinner("正在创建标注副本..."):
                    success = annotate_excel_with_performance(file_path, selected_sheet, result_df, annotated_file)

                if success:
                    # 提供下载
                    with open(annotated_file, 'rb') as f:
                        st.download_button(
                            label="📥 下载标注后的Excel文件",
                            data=f.read(),
                            file_name=os.path.basename(annotated_file),
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            key="download_annotated_copy"
                        )

                    # 成功提醒
                    st.balloons()
                    st.success("✅ 标注副本创建完成！请点击上方按钮下载文件")
                else:
                    st.error("❌ 创建标注副本失败")

        with col3:
            # 自定义晋级后缀
            promotion_suffix = st.text_input("晋级后缀", value="晋级", key="promotion_suffix")
            if st.button("🏆 创建晋级Sheet", key="create_promotion"):
                file_path = st.session_state['file_path']
                selected_sheet = st.session_state['selected_sheet']

                with st.spinner(f"正在创建{promotion_suffix}Sheet..."):
                    success = create_promotion_sheet(file_path, selected_sheet, result_df, promotion_suffix)

                if success:
                    st.balloons()
                    st.success(f"✅ {promotion_suffix}Sheet创建完成！")
                else:
                    st.error("❌ 创建失败")

if __name__ == "__main__":
    main()


