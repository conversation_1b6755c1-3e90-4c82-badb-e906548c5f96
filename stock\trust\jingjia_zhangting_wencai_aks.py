import streamlit as st
import pywencai
import pandas as pd
from datetime import datetime, timedelta
import akshare as ak

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)

exclude_concepts = ['融资融券', '国企改革']

@st.cache_data(ttl=3600, show_spinner="正在加载股票数据...")
def fetch_zt_data(target_date_str):
    """获取指定日期的涨停板数据"""
    try:
        query = f"非ST,{target_date_str}竞价涨停,{target_date_str}涨停，所属概念"
        return pywencai.get(
            query=query,
            sort_key=f'涨停封单额[{target_date_str}]',
            sort_order='desc',
            loop=True
        )
    except Exception as e:
        st.error(f"数据接口异常: {str(e)}")
        return pd.DataFrame()


def process_data(raw_df, target_date_str):
    """核心数据处理流程"""
    if raw_df.empty:
        return raw_df

    date_suffix = f'[{target_date_str}]'
    required_columns = {
        '股票代码': '代码',
        '股票简称': '名称',
        f'连续涨停天数{date_suffix}': '连板',
        f'涨停封单额{date_suffix}': '封单额(万)',
        f'涨停原因类别{date_suffix}': '涨停原因',
        '所属概念': '概念',
        f'首次涨停时间{date_suffix}': '首次涨停',
        f'最终涨停时间{date_suffix}': '最终涨停',
    }

    processed_df = raw_df[list(required_columns.keys())].copy()
    processed_df.rename(columns=required_columns, inplace=True)
    processed_df['封单额(万)'] = processed_df['封单额(万)'].astype(float)
    processed_df['晋级状态'] = processed_df['连板'].apply(
        lambda x: '晋级' if x > 1 else '首板')

    return processed_df.sort_values('封单额(万)', ascending=False)


def get_trade_dates():
    """获取交易日数据并格式化为两种形式"""
    try:
        trade_date_range = ak.tool_trade_date_hist_sina()
        trade_date_range['trade_date'] = pd.to_datetime(trade_date_range['trade_date']).dt.date
        trade_date_range['trade_date_str'] = trade_date_range['trade_date'].apply(
            lambda x: x.strftime('%Y%m%d'))
        return trade_date_range
    except Exception as e:
        st.error(f"获取交易日数据时出错: {str(e)}")
        return pd.DataFrame()


def app():
   # st.set_page_config(layout="wide", page_icon="📈")

    with st.spinner("正在加载交易日数据..."):
        trade_date_df = get_trade_dates()
        if trade_date_df.empty:
            st.error("无法获取交易日数据，请检查网络连接或稍后再试。")
            return

    # 日期处理逻辑
    trade_dates = trade_date_df['trade_date'].tolist()
    today = datetime.now().date()

    # 计算默认日期
    if trade_dates:
        if today in trade_dates:
            default_date = today
        else:
            past_dates = [d for d in trade_dates if d <= today]
            default_date = max(past_dates) if past_dates else trade_dates[-1]
    else:
        default_date = today - timedelta(days=1)

    # 日期选择组件
    selected_date = st.date_input(
        "📅 选择分析日期",
        value=default_date,
        min_value=trade_dates[0] if trade_dates else today - timedelta(days=30),
        max_value=trade_dates[-1] if trade_dates else today,
        key="date_selector"
    )

    # 日期有效性验证
    formatted_date_str = selected_date.strftime('%Y%m%d')
    if trade_dates and selected_date not in trade_dates:
        past_dates = [d for d in trade_dates if d <= selected_date]
        if past_dates:
            nearest_date = max(past_dates)
            st.warning(f"⚠️ 非交易日，已自动切换至最近交易日: {nearest_date.strftime('%Y%m%d')}")
            selected_date = nearest_date
            formatted_date_str = selected_date.strftime('%Y%m%d')
        else:
            st.error("无有效交易日可供选择")
            return

    # 数据加载
    with st.spinner(f"正在加载{formatted_date_str}数据..."):
        raw_data = fetch_zt_data(formatted_date_str)
        processed_data = process_data(raw_data, formatted_date_str)

    if processed_data.empty:
        st.error(f"{formatted_date_str} 无涨停数据")
        return

    # 界面展示
    st.title(f"{formatted_date_str} 竞价涨停分析看板")

    # 主表格展示
    st.subheader("竞价涨停个股榜单")

    st.dataframe(
        processed_data,
        use_container_width=True,
        hide_index=True,
        height=300
    )

    # 概念分析
    st.subheader("热点概念分布")
    if '概念' in processed_data.columns:
        concept_series = processed_data['概念'].str.split(';').explode()
        concept_series = concept_series[~concept_series.isin(exclude_concepts)]  # 核心过滤

        # 统计概念出现次数
        concept_df = concept_series.value_counts().reset_index()
        concept_df.columns = ["概念", "出现次数"]
        concept_df['强度'] = concept_df['出现次数'].apply(lambda x: '🔥' * min(x, 5))
        st.dataframe(concept_df, use_container_width=True, height=400)



if __name__ == "__main__":
    app()