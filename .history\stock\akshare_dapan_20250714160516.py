import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from pywencai import get


def get_recent_trading_days(n=10):
    """获取最近n个交易日（含今天）"""
    trade_date_df = ak.tool_trade_date_hist_sina()
    trade_date_df['trade_date'] = pd.to_datetime(trade_date_df['trade_date'])
    today = datetime.now().date()
    # 只保留今天及之前的交易日
    trade_date_df = trade_date_df[trade_date_df['trade_date'].dt.date <= today]
    # 按日期降序排序
    trade_date_df = trade_date_df.set_index('trade_date').sort_index(ascending=False)
    recent_days = trade_date_df.head(n).index
    return list(recent_days.strftime('%Y-%m-%d'))[::-1]  # 升序返回，使用标准日期格式


def get_market_statistics(date):
    """获取市场统计数据，使用问财接口"""
    try:
        query = f"{date}涨停家数，跌停家数，上涨家数，下跌家数，成交额"
        market_data = get(query=query, loop=True)  # 添加loop参数以处理可能的重试
        
        if isinstance(market_data, pd.DataFrame) and not market_data.empty:
            # 提取数据并进行类型转换
            stats = {
                'limit_up': int(market_data.loc[0, '涨停家数']),
                'limit_down': int(market_data.loc[0, '跌停家数']),
                'up_count': int(market_data.loc[0, '上涨家数']),
                'down_count': int(market_data.loc[0, '下跌家数']),
                'total_volume': float(market_data.loc[0, '成交额']) / 100  # 转换为亿
            }
            return stats
        else:
            print(f"未获取到{date}的市场统计数据或数据格式不正确")
            return None
            
    except Exception as e:
        print(f"获取市场统计数据失败: {str(e)}")
        return None


def get_index_data(symbol, date):
    """获取指数数据"""
    try:
        df = ak.stock_zh_index_daily_em(symbol=symbol)
        df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
        
        day_data = df[df['date'] == date]
        if day_data.empty:
            return None
            
        idx = list(day_data.index)[0]
        current_close = day_data.iloc[0]['close']
        
        # 计算涨跌幅
        if idx > 0:  # 确保不是第一条数据
            last_close = df.iloc[idx - 1]['close']
            change = (current_close - last_close) / last_close * 100
        else:
            change = 0
            
        return {'close': current_close, 'change': change}
        
    except Exception as e:
        print(f"获取指数数据失败: {str(e)}")
        return None


def get_market_data():
    """获取市场综合数据"""
    trading_days = get_recent_trading_days(10)
    print(f"获取到的交易日列表: {trading_days}")
    
    result = []
    for day in trading_days:
        print(f"\n处理日期: {day}")
        
        # 获取上证指数数据
        sh_data = get_index_data("sh000001", day)
        # 获取创业板指数据
        sz_data = get_index_data("sz399006", day)
        # 获取市场统计数据
        market_stats = get_market_statistics(day)
        
        if not all([sh_data, sz_data, market_stats]):
            print(f"日期 {day} 数据不完整，跳过")
            continue
            
        # 构建当日数据字典
        day_data = {
            '日期': day,
            '上证指数': 0,
            '上证涨跌幅%': 0,
            '创业板指': 0,
            '创业板涨跌幅%': 0,
            '沪深总成交(亿)': 0,
            '上涨家数': 0,
            '下跌家数': 0,
            '涨停数': 0,
            '跌停数': 0
        }
        
        # 安全地更新数据
        if sh_data:
            day_data.update({
                '上证指数': sh_data['close'],
                '上证涨跌幅%': sh_data['change']
            })
        
        if sz_data:
            day_data.update({
                '创业板指': sz_data['close'],
                '创业板涨跌幅%': sz_data['change']
            })
            
        if market_stats:
            day_data.update({
                '沪深总成交(亿)': market_stats['total_volume'],
                '上涨家数': market_stats['up_count'],
                '下跌家数': market_stats['down_count'],
                '涨停数': market_stats['limit_up'],
                '跌停数': market_stats['limit_down']
            })
        
        result.append(day_data)
    
    if not result:
        print("警告：未获取到任何数据！")
        return
        
    df = pd.DataFrame(result)
    print("\n最终数据预览:")
    print(df)
    
    # 保存到Excel
    df.to_excel('market_data_last10days.xlsx', index=False)
    print('\n数据已保存到 market_data_last10days.xlsx')

if __name__ == "__main__":
    get_market_data()