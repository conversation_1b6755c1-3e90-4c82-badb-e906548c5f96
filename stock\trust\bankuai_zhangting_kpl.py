import streamlit as st
import pywencai
import pandas as pd
from datetime import datetime



# 列对齐设置
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.max_colwidth', 100)


def app():
    st.title("股票涨停分析")

    # 日期选择
    selected_date = st.date_input("选择分析日期", datetime.today())
    date_str = selected_date.strftime("%Y%m%d")

    try:
        # 获取数据
        param = f"{date_str}涨停，非ST"
        df = pywencai.get(query=param, sort_key='成交金额', sort_order='desc')

        if df.empty:
            st.warning("当日没有涨停股票数据！")
            return

        # 列选择和处理
        selected_columns = [
            '股票代码', '股票简称', '最新价', '最新涨跌幅',f'连续涨停天数[{date_str}]',  f'首次涨停时间[{date_str}]',
                        f'最终涨停时间[{date_str}]', f'涨停封单量[{date_str}]',
                        f'涨停封单额[{date_str}]', f'涨停类型[{date_str}]',
                        f'几天几板[{date_str}]', f'涨停原因类别[{date_str}]',
                        f'a股市值(不含限售股)[{date_str}]'
        ]

        jj_df = df[selected_columns].copy()

        # 展开概念列表（关键修改）
        exploded_df = jj_df.assign(
            涨停原因=lambda x: x[f'涨停原因类别[{date_str}]'].str.split('+')
        ).explode('涨停原因')

        # 正确统计出现次数（包含重复个股）
        concept_counts = exploded_df.groupby('涨停原因').size().reset_index(name='出现次数')
        concept_counts = concept_counts.sort_values('出现次数', ascending=False)

        # 显示概念统计
        # st.subheader("涨停概念统计")
        # st.dataframe(concept_counts, use_container_width=True)

        # 分组显示数据
        st.subheader("按涨停原因分组")

        # 创建合并后的展示数据
        merged_df = pd.merge(
            exploded_df,
            concept_counts,
            on='涨停原因',
            how='left'
        ).sort_values(
            ['出现次数', '涨停原因', f'连续涨停天数[{date_str}]'],
            ascending=[False, True, False]
        )

        # 按出现次数排序分组
        for concept in concept_counts['涨停原因']:
            group_df = merged_df[merged_df['涨停原因'] == concept]
            with st.expander(f"{concept}（出现次数：{len(group_df)}）"):
                st.dataframe(
                    group_df[[
                        '股票代码', '股票简称', '最新价', '最新涨跌幅', '涨停原因',
                        f'首次涨停时间[{date_str}]',
                        f'最终涨停时间[{date_str}]', f'涨停封单量[{date_str}]',
                        f'涨停封单额[{date_str}]', f'涨停类型[{date_str}]',
                        f'几天几板[{date_str}]',
                        f'a股市值(不含限售股)[{date_str}]'
                    ]].reset_index(drop=True),
                    use_container_width=True
                )

        # 显示原始数据
        st.subheader("原始数据（未展开）")
        if st.checkbox("显示完整数据"):
            st.dataframe(jj_df, use_container_width=True)

    except Exception as e:
        st.error(f"获取数据时发生错误：{str(e)}")


if __name__ == "__main__":
    st.set_page_config(page_title="涨停分析", layout="wide")
    app()