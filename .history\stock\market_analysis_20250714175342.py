import streamlit as st
import akshare as ak
import pandas as pd
import numpy as np
import pywencai
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 设置页面
st.set_page_config(
    page_title="市场分析看板",
    page_icon="📈",
    layout="wide"
)

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)

@st.cache_data(ttl=3600)
def get_trading_calendar():
    """获取交易日历"""
    try:
        calendar = ak.tool_trade_date_hist_sina()
        # 转换为datetime.date对象
        calendar['trade_date'] = pd.to_datetime(calendar['trade_date']).dt.date
        return sorted(calendar['trade_date'].tolist())
    except Exception as e:
        st.error(f"获取交易日历失败: {str(e)}")
        return []

@st.cache_data(ttl=3600)
def get_index_data(symbol, end_date):
    """获取指数数据
    symbol: 指数代码，如 sh000001 (上证指数), sz399006 (创业板指)
    """
    try:
        # 计算开始日期（前10个交易日）
        trading_days = get_trading_calendar()
        end_date_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
        end_idx = trading_days.index(end_date_dt)
        start_idx = max(0, end_idx - 10)
        start_date = trading_days[start_idx].strftime('%Y-%m-%d')
        
        df = pd.DataFrame(ak.stock_zh_index_daily_em(symbol=symbol))
        if len(df) > 0:
            df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
            df = df[df['date'].between(start_date, end_date)].copy()
            df['change'] = df['close'].astype(float).pct_change() * 100
            result = df[['date', 'close', 'change']].copy()
            result.columns = ['日期', '收盘价', '涨跌幅%']
            return result
        return pd.DataFrame()
    except Exception as e:
        st.error(f"获取指数数据失败: {str(e)}")
        return pd.DataFrame()

@st.cache_data(ttl=3600)
def get_market_stats(date_str):
    """获取市场统计数据"""
    try:
        # 分开查询以提高成功率
        query1 = f"{date_str}沪深A股，上涨家数，下跌家数，成交额"
        query2 = f"{date_str}非ST，涨停家数，跌停家数"
        
        # 获取基本市场数据
        df1 = pywencai.get(query=query1, loop=True)
        if df1 is None:
            st.warning(f"获取市场基本数据失败: {date_str}")
            return None
            
        # 获取涨跌停数据
        df2 = pywencai.get(query=query2, loop=True)
        if df2 is None:
            st.warning(f"获取涨跌停数据失败: {date_str}")
            return None
            
        # 合并数据
        market_data = {
            '上涨家数': int(df1['上涨家数'].iloc[0]) if '上涨家数' in df1.columns else 0,
            '下跌家数': int(df1['下跌家数'].iloc[0]) if '下跌家数' in df1.columns else 0,
            '涨停家数': int(df2['涨停家数'].iloc[0]) if '涨停家数' in df2.columns else 0,
            '跌停家数': int(df2['跌停家数'].iloc[0]) if '跌停家数' in df2.columns else 0,
            '成交额': float(df1['成交额'].iloc[0]) if '成交额' in df1.columns else 0
        }
        
        return pd.DataFrame([market_data])
    except Exception as e:
        st.error(f"获取市场统计数据失败: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def get_continuous_limit_up(date_str):
    """获取连板数据"""
    try:
        query = f"非ST，{date_str}连续涨停天数，涨停原因，涨停封单额"
        df = pywencai.get(query=query, loop=True)
        if df.empty:
            return None
        return df
    except Exception as e:
        st.error(f"获取连板数据失败: {str(e)}")
        return None

@st.cache_data(ttl=3600)
def get_concept_stats(date_str):
    """获取概念板块统计"""
    try:
        query = f"非ST，{date_str}涨停，概念板块"
        df = pywencai.get(query=query, loop=True)
        if df.empty:
            return None
        return df
    except Exception as e:
        st.error(f"获取概念统计失败: {str(e)}")
        return None

def calculate_advance_rates(df, date_str):
    """计算晋级率"""
    if df is None or df.empty:
        return 0, 0, 0
    
    days_col = f'连续涨停天数[{date_str}]'
    if days_col not in df.columns:
        return 0, 0, 0
    
    df[days_col] = pd.to_numeric(df[days_col], errors='coerce')
    
    # 计算各连板数量
    one_board = len(df[df[days_col] == 1])
    two_board = len(df[df[days_col] == 2])
    three_board = len(df[df[days_col] == 3])
    four_board = len(df[df[days_col] == 4])
    
    # 计算晋级率
    rate_1to2 = round(two_board / one_board * 100, 2) if one_board > 0 else 0
    rate_2to3 = round(three_board / two_board * 100, 2) if two_board > 0 else 0
    rate_3to4 = round(four_board / three_board * 100, 2) if three_board > 0 else 0
    
    return rate_1to2, rate_2to3, rate_3to4

def analyze_concepts(df, date_str):
    """分析概念板块数据"""
    if df is None or df.empty:
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    # 获取涨跌幅和涨跌停列
    change_col = '涨跌幅'
    limit_up_col = f'涨停类型[{date_str}]'
    limit_down_col = f'跌停类型[{date_str}]'
    concept_col = '所属概念'
    
    if not all(col in df.columns for col in [change_col, concept_col]):
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    # 展开概念
    df_exploded = df.assign(概念=df[concept_col].str.split(';')).explode('概念')
    
    # 计算涨幅最高的概念
    top_gain = df_exploded.groupby('概念')[change_col].mean().sort_values(ascending=False).head(5)
    
    # 计算跌幅最大的概念
    top_loss = df_exploded.groupby('概念')[change_col].mean().sort_values().head(5)
    
    # 计算涨停最多的概念
    if limit_up_col in df.columns:
        top_limit_up = df_exploded[df_exploded[limit_up_col].notna()].groupby('概念').size().sort_values(ascending=False).head(5)
    else:
        top_limit_up = pd.Series()
    
    # 计算跌停最多的概念
    if limit_down_col in df.columns:
        top_limit_down = df_exploded[df_exploded[limit_down_col].notna()].groupby('概念').size().sort_values(ascending=False).head(5)
    else:
        top_limit_down = pd.Series()
    
    return top_gain, top_loss, top_limit_up, top_limit_down

def main():
    st.title("📊 市场分析看板")
    
    # 日期选择
    trading_days = get_trading_calendar()
    if not trading_days:
        st.error("无法获取交易日历")
        return
    
    # 获取最新和最早的交易日期
    max_date = trading_days[-1]  # 使用排序后的最后一个日期
    min_date = max_date - timedelta(days=365)
    
    selected_date = st.date_input(
        "选择日期",
        value=max_date,
        min_value=min_date,
        max_value=max_date
    )
    
    # 获取数据
    with st.spinner("正在获取数据..."):
        # 获取指数数据
        date_str = selected_date.strftime('%Y-%m-%d')
        sh_df = get_index_data("sh000001", date_str)
        cyb_df = get_index_data("sz399006", date_str)
        
        # 合并指数数据
        if not sh_df.empty and not cyb_df.empty:
            sh_df = sh_df.rename(columns={'收盘价': '上证指数', '涨跌幅%': '上证涨跌幅%'})
            cyb_df = cyb_df.rename(columns={'收盘价': '创业板指', '涨跌幅%': '创业板涨跌幅%'})
            index_df = pd.merge(sh_df, cyb_df, on='日期')
            index_df = index_df.sort_values('日期', ascending=False)
        else:
            st.error("获取指数数据失败")
            return
        
        # 获取当日市场数据
        current_date = selected_date.strftime('%Y%m%d')
        market_stats = get_market_stats(current_date)
        continuous_limit_up = get_continuous_limit_up(current_date)
        concept_data = get_concept_stats(current_date)
        
        # 计算晋级率
        rate_1to2, rate_2to3, rate_3to4 = calculate_advance_rates(continuous_limit_up, latest_date)
        
        # 分析概念数据
        top_gain, top_loss, top_limit_up, top_limit_down = analyze_concepts(concept_data, latest_date)
    
    # 显示数据
    st.subheader("指数行情（近10个交易日）")
    st.dataframe(
        index_df,
        column_config={
            "日期": st.column_config.DateColumn("日期", format="YYYY-MM-DD"),
            "上证指数": st.column_config.NumberColumn("上证指数", format="%.2f"),
            "上证涨跌幅%": st.column_config.NumberColumn("上证涨跌幅%", format="%.2f%%"),
            "创业板指": st.column_config.NumberColumn("创业板指", format="%.2f"),
            "创业板涨跌幅%": st.column_config.NumberColumn("创业板涨跌幅%", format="%.2f%%")
        },
        hide_index=True
    )
    
    # 市场统计数据
    st.subheader("市场统计")
    if market_stats is not None and len(market_stats) > 0:
        cols = st.columns(5)
        with cols[0]:
            st.metric("上涨家数", int(market_stats['上涨家数'].iloc[0]) if '上涨家数' in market_stats.columns else '-')
        with cols[1]:
            st.metric("下跌家数", int(market_stats['下跌家数'].iloc[0]) if '下跌家数' in market_stats.columns else '-')
        with cols[2]:
            st.metric("涨停数量", int(market_stats['涨停家数'].iloc[0]) if '涨停家数' in market_stats.columns else '-')
        with cols[3]:
            st.metric("跌停数量", int(market_stats['跌停家数'].iloc[0]) if '跌停家数' in market_stats.columns else '-')
        with cols[4]:
            st.metric("成交额(亿)", round(float(market_stats['成交额'].iloc[0])/100000000, 2) if '成交额' in market_stats.columns else '-')
    else:
        st.warning("未获取到市场统计数据")
    
    # 连板统计
    st.subheader("连板数据")
    if continuous_limit_up is not None:
        cols = st.columns(3)
        with cols[0]:
            st.metric("一进二晋级率", f"{rate_1to2}%")
        with cols[1]:
            st.metric("二进三晋级率", f"{rate_2to3}%")
        with cols[2]:
            st.metric("三进四晋级率", f"{rate_3to4}%")
    
    # 概念板块分析
    st.subheader("概念板块分析")
    if not top_gain.empty or not top_loss.empty:
        col1, col2 = st.columns(2)
        with col1:
            st.write("涨幅最高的概念")
            st.dataframe(top_gain.reset_index().rename(columns={'概念': '板块名称', change_col: '平均涨幅'}))
        with col2:
            st.write("跌幅最大的概念")
            st.dataframe(top_loss.reset_index().rename(columns={'概念': '板块名称', change_col: '平均跌幅'}))
    
    if not top_limit_up.empty or not top_limit_down.empty:
        col1, col2 = st.columns(2)
        with col1:
            st.write("涨停最多的概念")
            st.dataframe(top_limit_up.reset_index().rename(columns={'概念': '板块名称', 0: '涨停家数'}))
        with col2:
            st.write("跌停最多的概念")
            st.dataframe(top_limit_down.reset_index().rename(columns={'概念': '板块名称', 0: '跌停家数'}))
    
    # 数据下载
    st.subheader("数据下载")
    if not index_df.empty:
        csv = index_df.to_csv(index=False)
        st.download_button(
            label="下载数据",
            data=csv,
            file_name=f"market_data_{start_date}_{end_date}.csv",
            mime="text/csv"
        )

if __name__ == "__main__":
    main()