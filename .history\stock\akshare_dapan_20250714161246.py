import akshare as ak
import pandas as pd
from datetime import datetime


def get_recent_trading_days(n=10):
    """获取最近n个交易日（含今天）"""
    df = ak.tool_trade_date_hist_sina()
    # 转换为pandas的datetime
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    today = datetime.now().date()
    
    # 过滤并排序
    df = df[df['trade_date'].dt.date <= today].copy()
    df = df.sort_values(by='trade_date', ascending=True)
    
    # 获取最近n天
    dates = df['trade_date'].tail(n).dt.strftime('%Y-%m-%d').values
    return list(dates)


def get_index_data(symbol, start_date=None, end_date=None):
    """获取指数数据
    symbol: 指数代码，如 sh000001 (上证指数), sz399006 (创业板指)
    """
    try:
        # 获取指数日线数据
        df = ak.stock_zh_index_daily_em(symbol=symbol)
        if isinstance(df, pd.DataFrame) and not df.empty:
            # 转换日期格式
            df['date'] = pd.to_datetime(df['date'])
            df['date'] = df['date'].dt.strftime('%Y-%m-%d')
            
            # 如果指定了日期范围，进行过滤
            if start_date and end_date:
                df = df[df['date'].between(start_date, end_date)].copy()
            
            if len(df) > 1:  # 确保有足够数据计算涨跌幅
                # 计算涨跌幅
                df['change'] = (df['close'] / df['close'].shift(1) - 1) * 100
                
                # 只保留需要的列并重命名
                result = df[['date', 'close', 'change']].copy()
                result.columns = ['日期', '收盘价', '涨跌幅%']
                return result
            
        return pd.DataFrame()
        
    except Exception as e:
        print(f"获取指数数据失败: {str(e)}")
        return pd.DataFrame()


def get_market_data():
    """获取市场综合数据"""
    # 获取最近10个交易日
    trading_days = get_recent_trading_days(10)
    if not trading_days:
        print("未获取到交易日数据")
        return
        
    start_date, end_date = trading_days[0], trading_days[-1]
    print(f"获取日期范围: {start_date} 至 {end_date}")
    
    # 获取上证指数数据
    sh_df = get_index_data("sh000001", start_date, end_date)
    # 获取创业板指数据
    cyb_df = get_index_data("sz399006", start_date, end_date)
    
    if len(sh_df) == 0 or len(cyb_df) == 0:
        print("获取指数数据失败")
        return
    
    # 合并数据
    result = pd.merge(sh_df, cyb_df, on='日期', suffixes=('_上证', '_创业板'))
    
    # 重命名列
    result.columns = ['日期', '上证指数', '上证涨跌幅%', '创业板指', '创业板涨跌幅%']
    
    print("\n最终数据预览:")
    print(result)
    
    # 保存到Excel
    result.to_excel('market_data_last10days.xlsx', index=False)
    print('\n数据已保存到 market_data_last10days.xlsx')

if __name__ == "__main__":
    get_market_data()