import streamlit as st
import akshare as ak
import pandas as pd
import pywencai
from datetime import datetime, timedelta

# 设置页面
st.set_page_config(
    page_title="市场基本面分析",
    page_icon="📈",
    layout="wide"
)

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)

@st.cache_data(ttl=3600)
def get_trade_dates():
    """获取交易日历"""
    try:
        trade_date_range = ak.tool_trade_date_hist_sina()
        trade_date_range['trade_date'] = pd.to_datetime(trade_date_range['trade_date']).dt.date
        return trade_date_range['trade_date'].tolist()
    except Exception as e:
        st.error(f"获取交易日数据时出错: {str(e)}")
        return []

@st.cache_data(ttl=3600)
def get_index_data(symbol, end_date):
    """获取指数数据
    symbol: 指数代码，如 sh000001 (上证指数), sz399006 (创业板指)
    """
    try:
        # 计算开始日期（前10个交易日）
        trading_days = get_trade_dates()
        end_date_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
        end_idx = trading_days.index(end_date_dt)
        start_idx = max(0, end_idx - 10)
        start_date = trading_days[start_idx].strftime('%Y-%m-%d')
        
        raw_data = ak.stock_zh_index_daily_em(symbol=symbol)
        if raw_data is not None and len(raw_data) > 0:
            df = pd.DataFrame(raw_data)
            df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
            df = df[df['date'].between(start_date, end_date)].copy()
            df['close'] = pd.to_numeric(df['close'], errors='coerce')
            
            # 计算涨跌幅
            closes = list(df['close'])
            changes = [0.0]  # 第一个值的涨跌幅为0
            for i in range(1, len(closes)):
                if closes[i-1] != 0:  # 避免除以0
                    change = (closes[i] - closes[i-1]) / closes[i-1] * 100
                    changes.append(change)
                else:
                    changes.append(0.0)
            
            result = pd.DataFrame({
                '日期': df['date'].tolist(),
                '收盘价': closes,
                '涨跌幅%': changes
            })
            return result
        return pd.DataFrame()
    except Exception as e:
        st.error(f"获取指数数据失败: {str(e)}")
        return pd.DataFrame()

@st.cache_data(ttl=3600)
def get_market_data(date, query_type):
    """获取市场数据"""
    query_map = {
        'limit_up': f"非ST,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,{date.strftime('%Y%m%d')}跌停",
        'up_count': f"非ST,{date.strftime('%Y%m%d')}上涨数量",
        'down_count': f"非ST,{date.strftime('%Y%m%d')}下跌数量",
        'amount': f"非ST,{date.strftime('%Y%m%d')}成交额"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            loop=True
        )
        return df if df is not None and not df.empty else None
    except Exception as e:
        st.error(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_market_stats(date):
    """获取市场统计数据"""
    try:
        # 获取各类数据
        limit_up_df = get_market_data(date, 'limit_up')
        limit_down_df = get_market_data(date, 'limit_down')
        up_count_df = get_market_data(date, 'up_count')
        down_count_df = get_market_data(date, 'down_count')
        amount_df = get_market_data(date, 'amount')
        
        # 整合数据
        market_data = {
            '上涨家数': len(up_count_df) if up_count_df is not None else 0,
            '下跌家数': len(down_count_df) if down_count_df is not None else 0,
            '涨停家数': len(limit_up_df) if limit_up_df is not None else 0,
            '跌停家数': len(limit_down_df) if limit_down_df is not None else 0,
            '成交额': float(amount_df['成交额'].sum()) if amount_df is not None and '成交额' in amount_df.columns else 0
        }
        
        return pd.DataFrame([market_data])
    except Exception as e:
        st.error(f"获取市场统计数据失败: {str(e)}")
        return None

def main():
    st.title("📊 市场基本面分析")
    
    # 日期选择
    trading_days = get_trade_dates()
    if not trading_days:
        st.error("无法获取交易日历")
        return
    
    # 获取最新和最早的交易日期
    today = datetime.now().date()
    max_date = trading_days[-1]  # 最新交易日
    min_date = max_date - timedelta(days=365)  # 一年前
    
    # 如果今天不是交易日，找到最近的交易日
    default_date = today
    if today not in set(trading_days):
        # 找到小于等于今天的最近交易日
        for day in reversed(trading_days):
            if day <= today:
                default_date = day
                break
        if default_date != today:
            st.info(f"今天不是交易日，已自动调整到最近的交易日: {default_date.strftime('%Y-%m-%d')}")
    
    # 获取用户选择的日期
    col1, col2 = st.columns([3, 1])
    with col1:
        user_date = st.date_input(
            "选择日期",
            value=default_date,
            min_value=min_date,
            max_value=max_date
        )
    with col2:
        is_trading_day = user_date in set(trading_days)
        st.metric(
            "是否为交易日",
            "是" if is_trading_day else "否",
            delta=None,
            delta_color="normal"
        )
    
    # 找到最近的交易日
    trading_days_set = set(trading_days)  # 转换为集合以提高查找效率
    
    if user_date in trading_days_set:
        selected_date = user_date
    else:
        # 如果选择的不是交易日，找到最近的交易日
        while user_date not in trading_days_set and user_date >= min_date:
            user_date -= timedelta(days=1)
        
        if user_date >= min_date:
            selected_date = user_date
            st.info(f"已自动调整到最近的交易日: {selected_date.strftime('%Y-%m-%d')}")
        else:
            selected_date = max_date
            st.warning("未找到最近的交易日，已调整到最新交易日")
    
    # 获取数据
    with st.spinner("正在获取数据..."):
        # 获取指数数据
        date_str = selected_date.strftime('%Y-%m-%d')
        sh_df = get_index_data("sh000001", date_str)
        cyb_df = get_index_data("sz399006", date_str)
        
        # 合并指数数据
        if len(sh_df) > 0 and len(cyb_df) > 0:
            sh_df = pd.DataFrame(sh_df).rename(columns={'收盘价': '上证指数', '涨跌幅%': '上证涨跌幅%'})
            cyb_df = pd.DataFrame(cyb_df).rename(columns={'收盘价': '创业板指', '涨跌幅%': '创业板涨跌幅%'})
            index_df = pd.merge(sh_df, cyb_df, on='日期')
            index_df = index_df.sort_values('日期', ascending=False)
        else:
            st.error("获取指数数据失败")
            return
        
        # 获取当日市场数据
        market_stats = get_market_stats(selected_date)
    
    # 显示数据
    st.subheader("指数行情（近10个交易日）")
    st.dataframe(
        index_df,
        column_config={
            "日期": st.column_config.DateColumn("日期", format="YYYY-MM-DD"),
            "上证指数": st.column_config.NumberColumn("上证指数", format="%.2f"),
            "上证涨跌幅%": st.column_config.NumberColumn("上证涨跌幅%", format="%.2f%%"),
            "创业板指": st.column_config.NumberColumn("创业板指", format="%.2f"),
            "创业板涨跌幅%": st.column_config.NumberColumn("创业板涨跌幅%", format="%.2f%%")
        },
        hide_index=True
    )
    
    # 市场统计数据
    st.subheader("市场统计")
    if market_stats is not None and len(market_stats) > 0:
        cols = st.columns(5)
        with cols[0]:
            st.metric("上涨家数", int(market_stats['上涨家数'].iloc[0]) if '上涨家数' in market_stats.columns else '-')
        with cols[1]:
            st.metric("下跌家数", int(market_stats['下跌家数'].iloc[0]) if '下跌家数' in market_stats.columns else '-')
        with cols[2]:
            st.metric("涨停数量", int(market_stats['涨停家数'].iloc[0]) if '涨停家数' in market_stats.columns else '-')
        with cols[3]:
            st.metric("跌停数量", int(market_stats['跌停家数'].iloc[0]) if '跌停家数' in market_stats.columns else '-')
        with cols[4]:
            st.metric("成交额(亿)", round(float(market_stats['成交额'].iloc[0])/100000000, 2) if '成交额' in market_stats.columns else '-')
    else:
        st.warning("未获取到市场统计数据")
    
    # 数据下载
    st.subheader("数据下载")
    if len(index_df) > 0:
        csv = index_df.to_csv(index=False)
        st.download_button(
            label="下载数据",
            data=csv,
            file_name=f"market_data_{selected_date.strftime('%Y%m%d')}.csv",
            mime="text/csv"
        )

if __name__ == "__main__":
    main()