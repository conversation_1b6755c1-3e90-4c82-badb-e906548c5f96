#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试板块统计和晋级率计算
"""

import pandas as pd
from datetime import datetime, date

def test_promotion_calculation():
    """测试晋级率计算逻辑"""
    print("=" * 50)
    print("测试板块统计和晋级率计算")
    print("=" * 50)
    
    # 模拟前一日数据
    previous_data = {
        '股票代码': ['000001', '000002', '000003', '000004', '000005', '000006', '000007', '000008'],
        '股票简称': ['股票1', '股票2', '股票3', '股票4', '股票5', '股票6', '股票7', '股票8'],
        '连续涨停天数[20241201]': [1, 1, 2, 2, 3, 4, 5, 7]  # 1板:2只, 2板:2只, 3板:1只, 4板:1只, 5板:1只, 7板:1只
    }
    previous_df = pd.DataFrame(previous_data)
    
    # 模拟当前日数据
    current_data = {
        '股票代码': ['000001', '000003', '000004', '000006', '000008'],  # 部分股票继续涨停
        '股票简称': ['股票1', '股票3', '股票4', '股票6', '股票8'],
        '连续涨停天数[20241202]': [2, 3, 3, 5, 8]  # 1进2:1只, 2进3:1只, 3进4:0只, 4进5:1只, 7进8:1只
    }
    current_df = pd.DataFrame(current_data)
    
    print("前一日数据:")
    print(previous_df[['股票简称', '连续涨停天数[20241201]']])
    print("\n当前日数据:")
    print(current_df[['股票简称', '连续涨停天数[20241202]']])
    
    # 手动计算预期结果
    print("\n预期统计结果:")
    print("当前板块数量:")
    print("  1板数量: 0")
    print("  2板数量: 1 (000001)")
    print("  3板数量: 2 (000003, 000004)")
    print("  4板数量: 0")
    print("  5板数量: 1 (000006)")
    print("  6板数量: 0")
    print("  6板以上数量: 1 (000008)")
    
    print("\n预期晋级率:")
    print("  总晋级率: 62.5% (5/8)")
    print("  1进2晋级率: 50.0% (1/2)")
    print("  2进3晋级率: 50.0% (1/2)")
    print("  3进4晋级率: 0.0% (0/1)")
    print("  4进5晋级率: 100.0% (1/1)")
    print("  5进6晋级率: 0.0% (0/1)")
    print("  6进7晋级率: 0.0% (0/0)")
    print("  6板以上晋级率: 100.0% (1/1)")
    
    return previous_df, current_df

def simulate_calculation(current_df, previous_df):
    """模拟计算过程"""
    print("\n" + "=" * 50)
    print("模拟计算过程")
    print("=" * 50)
    
    current_days_col = '连续涨停天数[20241202]'
    previous_days_col = '连续涨停天数[20241201]'
    
    stats = []

    # 处理数据
    current_df_copy = current_df.copy()
    current_df_copy[current_days_col] = pd.to_numeric(current_df_copy[current_days_col], errors='coerce')
    current_df_copy[current_days_col] = current_df_copy[current_days_col].fillna(1)

    previous_df_copy = previous_df.copy()
    previous_df_copy[previous_days_col] = pd.to_numeric(previous_df_copy[previous_days_col], errors='coerce')
    previous_df_copy[previous_days_col] = previous_df_copy[previous_days_col].fillna(1)

    # 统计当前各板块数量（1-6板及6板以上）
    for i in range(1, 7):  # 1-6板
        count = len(current_df_copy[current_df_copy[current_days_col] == i])
        stats.append({'指标': f'{i}板数量', '数值': count})
        print(f"{i}板数量: {count}")
    
    # 统计6板以上
    count_6plus = len(current_df_copy[current_df_copy[current_days_col] >= 7])
    stats.append({'指标': '6板以上数量', '数值': count_6plus})
    print(f"6板以上数量: {count_6plus}")

    # 计算总晋级率
    total_prev = len(previous_df_copy)
    total_curr_promoted = len(current_df_copy[current_df_copy[current_days_col] > 1])
    total_rate = round(total_curr_promoted / total_prev * 100, 2) if total_prev > 0 else 0
    stats.append({'指标': '总晋级率', '数值': f"{total_rate}%"})
    print(f"总晋级率: {total_rate}% ({total_curr_promoted}/{total_prev})")

    # 计算各板晋级率（1-6板，即使为0也显示）
    print("\n各板晋级率:")
    for i in range(1, 7):  # 1进2, 2进3, ..., 6进7
        prev_count = len(previous_df_copy[previous_df_copy[previous_days_col] == i])
        curr_count = len(current_df_copy[current_df_copy[current_days_col] == i + 1])
        
        rate = round(curr_count / prev_count * 100, 2) if prev_count > 0 else 0
        stats.append({'指标': f'{i}进{i+1}晋级率', '数值': f"{rate}% ({curr_count}/{prev_count})"})
        print(f"{i}进{i+1}晋级率: {rate}% ({curr_count}/{prev_count})")

    # 计算6板以上晋级率
    prev_count_6plus = len(previous_df_copy[previous_df_copy[previous_days_col] >= 7])
    curr_count_7plus = len(current_df_copy[current_df_copy[current_days_col] >= 8])
    
    rate_6plus = round(curr_count_7plus / prev_count_6plus * 100, 2) if prev_count_6plus > 0 else 0
    stats.append({'指标': '6板以上晋级率', '数值': f"{rate_6plus}% ({curr_count_7plus}/{prev_count_6plus})"})
    print(f"6板以上晋级率: {rate_6plus}% ({curr_count_7plus}/{prev_count_6plus})")

    return pd.DataFrame(stats)

def main():
    """主测试函数"""
    print("🚀 测试板块统计和晋级率计算逻辑")
    
    # 生成测试数据
    previous_df, current_df = test_promotion_calculation()
    
    # 模拟计算
    result_df = simulate_calculation(current_df, previous_df)
    
    print("\n" + "=" * 50)
    print("最终输出结果")
    print("=" * 50)
    for _, row in result_df.iterrows():
        print(f"{row['指标']}: {row['数值']}")
    
    print("\n✅ 测试完成！")
    print("📝 注意：即使晋级率为0%，也会显示在结果中")

if __name__ == "__main__":
    main()
