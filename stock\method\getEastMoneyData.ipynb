{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-02T09:15:05.851899Z", "start_time": "2025-03-02T09:15:05.238579Z"}}, "source": ["import requests\n", "import pandas as pd"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-02T09:15:05.856954Z", "start_time": "2025-03-02T09:15:05.851899Z"}}, "cell_type": "code", "source": ["# 东方财富网 API URL\n", "url = \"http://72.push2.eastmoney.com/api/qt/clist/get\"\n", "df = None"], "id": "b0b158efc15026bd", "outputs": [], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-02T09:15:09.362697Z", "start_time": "2025-03-02T09:15:05.856954Z"}}, "cell_type": "code", "source": ["for X in pd.Series(range(1, 101)):  # 由于不知道页数上限，暂定遍历100次    \n", "    # 请求参数    \n", "    params = {        \n", "        \"pn\": X,  # 对页码进行遍历        \n", "        \"pz\": \"200\",  # 每页数量改为200        \n", "        \"po\": \"1\",        \n", "        \"np\": \"1\",        \n", "        \"ut\": \"bd1d9ddb04089700cf9c27f6f7426281\",        \n", "        \"fltt\": \"2\",        \n", "        \"invt\": \"2\",        \n", "        \"fid\": \"f3\",        \n", "        \"fs\": \"m:0 t:6,m:0 t:80,m:1 t:2,m:1 t:23\",  # A 股市场代码, m:0 t:81可添加北交所        \n", "        \"fields\": \"f12,f14,f2,f3,f4,f5,f6,f7,f8,f9,f10,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152\",# 需要获取的字段        \n", "        \"_\": \"1627379537641\"    }\n", "    \n", "    # 发送请求    \n", "    response = requests.get(url, params=params)    \n", "    data = response.json()\n", "    try:        \n", "        # 解析数据        \n", "        stock_list = data[\"data\"][\"diff\"]        \n", "        df = pd.concat([pd.DataFrame(stock_list), df], axis=0, ignore_index=True)   # 将新页码的数据叠加到旧页码的合集中    \n", "    except Exception as e:        \n", "        break"], "id": "d92efb45284b1a74", "outputs": [], "execution_count": 3}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-02T09:15:09.368528Z", "start_time": "2025-03-02T09:15:09.362697Z"}}, "cell_type": "code", "source": ["# 字段说明\n", "field_mapping = {    \n", "    \"f12\": \"code\",  # 股票代码    \n", "    \"f14\": \"name\",  # 股票名称    \n", "    \"f2\": \"latest_price\",  # 最新价    \n", "    \"f3\": \"change_percent\",  # 涨跌幅    \n", "    \"f4\": \"change_amount\",  # 涨跌额    \n", "    \"f5\": \"volume\",  # 成交量（手）    \n", "    \"f6\": \"turnover\",  # 成交额（万元）    \n", "    \"f7\": \"amplitude\",  # 振幅    \n", "    \"f8\": \"turnover_rate\",  # 换手率    \n", "    \"f9\": \"pe_ratio\",  # 市盈率    \n", "    \"f10\": \"pb_ratio\",  # 市净率    \n", "    \"f15\": \"high\",  # 最高价    \n", "    \"f16\": \"low\",  # 最低价    \n", "    \"f17\": \"open\",  # 开盘价    \n", "    \"f18\": \"close\",  # 昨收价    \n", "    \"f20\": \"total_market_cap\",  # 总市值    \n", "    \"f21\": \"circulating_market_cap\",  # 流通市值    \n", "    \"f23\": \"dividend_yield\",  # 股息率    \n", "    \"f24\": \"eps\",  # 每股收益    \n", "    \"f25\": \"net_asset_per_share\",  # 每股净资产    \n", "    \"f22\": \"volume_ratio\",  # 量比    \n", "    \"f11\": \"pe_ratio_ttm\",  # 市盈率（TTM）    \n", "    \"f62\": \"main_net_inflow\",  # 主力净流入    \n", "    \"f128\": \"industry\",  # 行业    \n", "    \"f136\": \"listing_date\",  # 上市日期    \n", "    \"f115\": \"pe_ratio_dynamic\",  # 动态市盈率    \n", "    \"f152\": \"pb_ratio_dynamic\",  # 动态市净率\n", "}\n"], "id": "ff024d00a62d184", "outputs": [], "execution_count": 4}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-02T09:15:09.380591Z", "start_time": "2025-03-02T09:15:09.368528Z"}}, "cell_type": "code", "source": ["# 重命名列\n", "df = df.rename(columns=field_mapping)"], "id": "8c6b6fd34eadab", "outputs": [], "execution_count": 5}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-02T09:15:14.248061Z", "start_time": "2025-03-02T09:15:12.199871Z"}}, "cell_type": "code", "source": ["# 保存到 XLSX 文件\n", "df.to_excel(\"a_stock_list_with_info.xlsx\", sheet_name='Sheet1', index=False)"], "id": "2b0b477b3be099a0", "outputs": [], "execution_count": 6}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "8527aad753185830"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}