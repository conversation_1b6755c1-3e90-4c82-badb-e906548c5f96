#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于AI智能分类的涨停股票归类工具
使用DeepSeek或其他AI API进行概念分类
"""

import pywencai
import pandas as pd
from datetime import datetime, date
import warnings
import os
import json
import requests
warnings.filterwarnings('ignore')

def create_output_directory():
    """创建输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(script_dir, "ai_limit_up_output")
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    return output_dir

def get_limit_up_stocks(query_date):
    """获取涨停股票数据"""
    try:
        print(f"🔍 查询 {query_date.strftime('%Y%m%d')} 涨停股票数据...")
        
        query = f"非ST,沪深主板,{query_date.strftime('%Y%m%d')}涨停"
        print(f"🔍 执行查询: {query}")
        
        df = pywencai.get(
            query=query,
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        
        if df is None or df.empty:
            print("❌ 查询结果为空")
            return None
            
        print(f"✅ 获取到 {len(df)} 只涨停股票")
        return df
        
    except Exception as e:
        print(f"❌ 获取涨停股票数据失败: {str(e)}")
        return None

def call_deepseek_api(prompt, api_key=None):
    """
    调用DeepSeek R1 API进行智能分类

    Args:
        prompt: 提示词
        api_key: API密钥

    Returns:
        str: AI返回的分类结果
    """
    try:
        # DeepSeek API配置
        url = "https://api.deepseek.com/v1/chat/completions"

        # 如果没有提供API密钥，使用环境变量或默认值
        if not api_key:
            api_key = os.getenv('DEEPSEEK_API_KEY', '***********************************').strip()

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }

        data = {
            "model": "deepseek-r1",  # 使用deepseek-r1模型
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的股票概念分类专家，擅长根据股票名称和涨停原因进行概念归类。请严格按照用户要求的格式输出结果。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 3000
        }

        response = requests.post(url, headers=headers, json=data, timeout=60)

        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"❌ DeepSeek R1 API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None

    except Exception as e:
        print(f"❌ DeepSeek R1 API调用失败: {str(e)}")
        return None



def create_classification_prompt(stocks_data, user_keywords):
    """
    创建AI分类的提示词，按连板数分组

    Args:
        stocks_data: 股票数据DataFrame
        user_keywords: 用户提供的关键词列表

    Returns:
        str: 构建的提示词
    """
    # 按连板数分组准备股票信息
    board_groups = {}
    for _, row in stocks_data.iterrows():
        stock_name = row.get('股票简称', '')
        reason = row.get('涨停原因', '无')
        board_num = row.get('连板数', 1)

        if board_num not in board_groups:
            board_groups[board_num] = []

        board_groups[board_num].append({
            'name': stock_name,
            'reason': reason
        })

    # 按连板数降序排列
    sorted_boards = sorted(board_groups.keys(), reverse=True)

    # 构建股票信息字符串
    stock_info_lines = []
    for board_num in sorted_boards:
        stocks = board_groups[board_num]
        stock_info_lines.append(f"\n{board_num}板股票:")
        for stock in stocks:
            stock_info_lines.append(f"  - {stock['name']}: {stock['reason']}")

    stock_list = "\n".join(stock_info_lines)
    keywords_str = "、".join(user_keywords)

    prompt = f"""
请根据用户提供的关键词对涨停股票进行智能分类，要求按连板数分组输出：

用户关键词: {keywords_str}

涨停股票信息（按连板数分组）:
{stock_list}

请按照以下严格要求进行分类和输出：

1. 输出格式必须是表格形式，第一列为连板数，后面各列为用户关键词
2. 每个连板数下的股票要根据涨停原因智能分配到对应的关键词列
3. 如果某个股票不属于任何用户关键词，归类到"其他"列
4. 按连板数从高到低排列（如6板、5板、4板...）
5. 每个单元格内的股票用换行分隔

输出格式示例：
连板数 | {keywords_str.replace('、', ' | ')} | 其他
6板   | 股票A | 股票B |      | 股票C
5板   |      | 股票D | 股票E |
...

请严格按照此格式输出，不要添加任何额外说明或标记。
"""

    return prompt

def parse_ai_classification(ai_response, user_keywords):
    """
    解析AI返回的表格格式分类结果

    Args:
        ai_response: AI返回的文本
        user_keywords: 用户关键词列表

    Returns:
        dict: 分类结果字典，格式为 {board_num: {keyword: [stocks]}}
    """
    try:
        classification = {}

        if not ai_response:
            return classification

        lines = ai_response.strip().split('\n')

        # 查找表格数据行
        table_lines = []
        for line in lines:
            line = line.strip()
            if line and ('板' in line or '|' in line) and not line.startswith('连板数'):
                table_lines.append(line)

        # 解析表格行
        for line in table_lines:
            if '板' in line:
                # 分割表格列
                if '|' in line:
                    parts = [p.strip() for p in line.split('|')]
                else:
                    # 尝试其他分隔符
                    parts = [p.strip() for p in line.split('\t') if p.strip()]
                    if len(parts) == 1:
                        parts = [p.strip() for p in line.split('  ') if p.strip()]

                if len(parts) >= 2:
                    # 提取连板数
                    board_str = parts[0]
                    board_num = None
                    for char in board_str:
                        if char.isdigit():
                            board_num = int(char)
                            break

                    if board_num is not None:
                        classification[board_num] = {}

                        # 解析各关键词列的股票
                        for i, keyword in enumerate(user_keywords):
                            col_index = i + 1
                            if col_index < len(parts):
                                stocks_str = parts[col_index].strip()
                                if stocks_str and stocks_str != '':
                                    # 解析股票名称
                                    stocks = []
                                    for sep in ['\n', '、', ',', '，', ' ']:
                                        if sep in stocks_str:
                                            stocks = [s.strip() for s in stocks_str.split(sep) if s.strip()]
                                            break

                                    if not stocks and stocks_str:
                                        stocks = [stocks_str]

                                    classification[board_num][keyword] = stocks
                                else:
                                    classification[board_num][keyword] = []
                            else:
                                classification[board_num][keyword] = []

                        # 处理"其他"列
                        if len(parts) > len(user_keywords) + 1:
                            other_col = parts[len(user_keywords) + 1].strip()
                            if other_col:
                                stocks = []
                                for sep in ['\n', '、', ',', '，', ' ']:
                                    if sep in other_col:
                                        stocks = [s.strip() for s in other_col.split(sep) if s.strip()]
                                        break

                                if not stocks and other_col:
                                    stocks = [other_col]

                                classification[board_num]['其他'] = stocks
                            else:
                                classification[board_num]['其他'] = []

        return classification

    except Exception as e:
        print(f"❌ 解析AI分类结果失败: {str(e)}")
        print(f"AI响应内容: {ai_response}")
        return {}

def ai_classify_limit_up_stocks(query_date, user_keywords, api_key=None, show_results=True):
    """
    使用AI进行涨停股票智能分类
    
    Args:
        query_date: 查询日期
        user_keywords: 用户提供的关键词列表
        ai_provider: AI提供商 ('deepseek' 或 'kimi')
        api_key: API密钥
        show_results: 是否显示结果
        
    Returns:
        dict: 分类结果
    """
    try:
        # 处理日期格式
        if isinstance(query_date, str):
            if len(query_date) == 8:
                selected_date = datetime.strptime(query_date, '%Y%m%d').date()
            else:
                selected_date = datetime.strptime(query_date, '%Y-%m-%d').date()
        else:
            selected_date = query_date
            
        if show_results:
            print(f"🤖 AI智能分类 {selected_date.strftime('%Y-%m-%d')} 涨停股票...")
            print(f"🏷️ 用户关键词: {', '.join(user_keywords)}")
            print(f"🔧 AI模型: DeepSeek R1")
            
        # 获取涨停股票数据
        limit_up_df = get_limit_up_stocks(selected_date)
        
        if limit_up_df is None:
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'keywords': user_keywords,
                'classification': {},
                'raw_data': pd.DataFrame(),
                'success': False,
                'error': '未获取到涨停股票数据'
            }
            
        # 处理股票数据
        date_str = selected_date.strftime('%Y%m%d')
        days_col = f'连续涨停天数[{date_str}]'
        reason_col = f'涨停原因类别[{date_str}]'
        
        # 查找相似列
        if days_col not in limit_up_df.columns:
            possible_days_cols = [col for col in limit_up_df.columns if '连续涨停' in col or '涨停天数' in col]
            days_col = possible_days_cols[0] if possible_days_cols else None
            
        if reason_col not in limit_up_df.columns:
            possible_reason_cols = [col for col in limit_up_df.columns if '涨停原因' in col or '概念' in col]
            reason_col = possible_reason_cols[0] if possible_reason_cols else None
            
        # 准备数据
        df_work = limit_up_df.copy()
        
        if days_col:
            df_work['连板数'] = pd.to_numeric(df_work[days_col], errors='coerce').fillna(1).astype(int)
        else:
            df_work['连板数'] = 1
            
        if reason_col:
            df_work['涨停原因'] = df_work[reason_col].fillna('无').astype(str)
        else:
            df_work['涨停原因'] = '无'
            
        # 创建AI分类提示词
        prompt = create_classification_prompt(df_work, user_keywords)
        
        if show_results:
            print("🤖 正在调用DeepSeek R1进行智能分类...")
            
        # 调用DeepSeek R1 API
        ai_response = call_deepseek_api(prompt, api_key)
            
        if not ai_response:
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'keywords': user_keywords,
                'classification': {},
                'raw_data': df_work,
                'success': False,
                'error': 'AI API调用失败'
            }
            
        # 解析AI分类结果
        classification = parse_ai_classification(ai_response, user_keywords)

        if show_results:
            print("\n🎯 AI智能分类结果:")
            print("=" * 80)

            # 显示表格格式结果
            if classification:
                # 构建表头
                header = ["连板数"] + user_keywords + ["其他"]
                col_widths = [8] + [12] * len(user_keywords) + [12]

                # 打印表头
                header_line = ""
                for i, col in enumerate(header):
                    header_line += f"{col:<{col_widths[i]}}"
                print(header_line)
                print("-" * 80)

                # 按连板数降序显示
                for board_num in sorted(classification.keys(), reverse=True):
                    board_data = classification[board_num]

                    # 计算该连板数下最多的股票数量
                    max_stocks = max([len(stocks) for stocks in board_data.values()]) if board_data else 0

                    # 显示每一行
                    for row_idx in range(max(1, max_stocks)):
                        if row_idx == 0:
                            line = f"{board_num}板{'':<4}"
                        else:
                            line = f"{'':8}"

                        # 显示各关键词列的股票
                        for keyword in user_keywords:
                            stocks = board_data.get(keyword, [])
                            if row_idx < len(stocks):
                                line += f"{stocks[row_idx]:<12}"
                            else:
                                line += f"{'':12}"

                        # 显示其他列
                        other_stocks = board_data.get('其他', [])
                        if row_idx < len(other_stocks):
                            line += f"{other_stocks[row_idx]:<12}"
                        else:
                            line += f"{'':12}"

                        print(line)

                    print()  # 连板数之间空行

            print(f"\n📊 分类统计:")
            total_classified = 0
            for board_data in classification.values():
                for stocks in board_data.values():
                    total_classified += len(stocks)
            print(f"   总分类股票: {total_classified}只")
            print(f"   连板数分组: {len(classification)}个")
            
        return {
            'date': selected_date.strftime('%Y-%m-%d'),
            'keywords': user_keywords,
            'classification': classification,
            'raw_data': df_work,
            'ai_response': ai_response,
            'success': True
        }
        
    except Exception as e:
        error_msg = f"AI智能分类失败: {str(e)}"
        if show_results:
            print(f"❌ {error_msg}")
            
        return {
            'date': str(query_date),
            'keywords': user_keywords,
            'classification': {},
            'raw_data': pd.DataFrame(),
            'success': False,
            'error': error_msg
        }

def export_ai_classification(result, output_dir=None):
    """导出AI分类结果到Excel（按连板数分组格式）"""
    try:
        if not result['success']:
            print("❌ 无法导出，AI分类失败")
            return None

        if not output_dir:
            output_dir = create_output_directory()

        date_str = result['date'].replace('-', '')
        filename = f"ai_classification_{date_str}.xlsx"
        excel_path = os.path.join(output_dir, filename)

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # AI分类结果工作表（按连板数分组）
            classification_data = []

            # 表头
            header = ["连板数"] + result['keywords'] + ["其他"]
            classification_data.append(header)

            # 按连板数降序处理
            classification = result['classification']
            for board_num in sorted(classification.keys(), reverse=True):
                board_data = classification[board_num]

                # 计算该连板数下最多的股票数量
                max_stocks = max([len(stocks) for stocks in board_data.values()]) if board_data else 0

                # 为每个连板数创建多行
                for row_idx in range(max(1, max_stocks)):
                    row = []

                    # 第一列：连板数（只在第一行显示）
                    if row_idx == 0:
                        row.append(f"{board_num}板")
                    else:
                        row.append("")

                    # 各关键词列
                    for keyword in result['keywords']:
                        stocks = board_data.get(keyword, [])
                        if row_idx < len(stocks):
                            row.append(stocks[row_idx])
                        else:
                            row.append("")

                    # 其他列
                    other_stocks = board_data.get('其他', [])
                    if row_idx < len(other_stocks):
                        row.append(other_stocks[row_idx])
                    else:
                        row.append("")

                    classification_data.append(row)

                # 连板数之间添加空行
                if board_num != min(classification.keys()):
                    classification_data.append([""] * len(header))

            # 创建DataFrame并导出
            classification_df = pd.DataFrame(classification_data)
            classification_df.to_excel(writer, sheet_name='AI智能分类', index=False, header=False)

            # 统计信息工作表
            stats_data = [['连板数', '关键词', '股票数量']]
            for board_num in sorted(classification.keys(), reverse=True):
                board_data = classification[board_num]
                for keyword, stocks in board_data.items():
                    stats_data.append([f"{board_num}板", keyword, len(stocks)])

            stats_df = pd.DataFrame(stats_data[1:], columns=stats_data[0])
            stats_df.to_excel(writer, sheet_name='分类统计', index=False)

            # 原始数据工作表
            if not result['raw_data'].empty:
                key_columns = ['股票简称', '股票代码', '连板数', '涨停原因']
                for col in result['raw_data'].columns:
                    if any(keyword in col for keyword in ['收盘价', '涨跌幅', '成交额', '市值']):
                        if col not in key_columns:
                            key_columns.append(col)

                available_columns = [col for col in key_columns if col in result['raw_data'].columns]
                raw_df = result['raw_data'][available_columns]
                raw_df.to_excel(writer, sheet_name='原始数据', index=False)

        print(f"✅ AI分类结果已导出: {excel_path}")
        return excel_path

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        return None

def get_recent_trading_dates(days=5):
    """获取最近几个交易日"""
    from datetime import timedelta

    dates = []
    current = date.today()

    while len(dates) < days:
        if current.weekday() < 5:  # 0-4 是周一到周五
            dates.append(current)
        current -= timedelta(days=1)

    return sorted(dates, reverse=True)

def main():
    """主函数"""
    import sys

    print("🤖 AI智能涨停分类工具")
    print("=" * 50)

    if len(sys.argv) > 1:
        # 命令行模式
        query_date = sys.argv[1]

        # 检查是否提供了关键词
        if len(sys.argv) > 2:
            keywords_str = sys.argv[2]
            keywords = [k.strip() for k in keywords_str.split(',') if k.strip()]
        else:
            keywords = ["AI", "新能源", "医药", "军工", "消费"]  # 默认关键词

        print(f"📅 查询日期: {query_date}")
        print(f"🏷️ 分类关键词: {', '.join(keywords)}")

        result = ai_classify_limit_up_stocks(query_date, keywords)

        if result['success']:
            export_path = export_ai_classification(result)
            print(f"\n✅ AI智能分类完成")
            if export_path:
                print(f"📄 结果文件: {export_path}")
        else:
            print(f"\n❌ AI分类失败: {result.get('error', '未知错误')}")
            if 'API' in result.get('error', ''):
                print("💡 请检查DeepSeek API密钥设置")
    else:
        # 交互模式
        print("请按照提示进行操作：")

        # 选择日期
        print("\n📅 选择查询日期:")
        print("1. 输入指定日期")
        print("2. 选择最近交易日")

        date_choice = input("请选择 (1/2): ").strip()

        if date_choice == '2':
            recent_dates = get_recent_trading_dates(5)
            print("\n📅 最近交易日:")
            for i, d in enumerate(recent_dates, 1):
                weekday = ['周一','周二','周三','周四','周五','周六','周日'][d.weekday()]
                print(f"   {i}. {d.strftime('%Y-%m-%d')} ({weekday})")

            date_idx = input("请选择日期 (1-5): ").strip()
            try:
                date_index = int(date_idx) - 1
                if 0 <= date_index < len(recent_dates):
                    query_date = recent_dates[date_index]
                else:
                    print("❌ 选择无效")
                    return
            except ValueError:
                print("❌ 请输入有效数字")
                return
        else:
            date_input = input("请输入日期 (YYYY-MM-DD 或 YYYYMMDD): ").strip()
            if not date_input:
                print("❌ 请输入有效日期")
                return
            query_date = date_input

        # 输入关键词
        print("\n🏷️ 输入分类关键词:")
        print("请输入您想要的分类关键词，用逗号分隔")
        print("例如: AI,新能源,医药,军工,消费")

        keywords_input = input("关键词: ").strip()
        if not keywords_input:
            keywords = ["AI", "新能源", "医药", "军工", "消费"]
            print(f"使用默认关键词: {', '.join(keywords)}")
        else:
            keywords = [k.strip() for k in keywords_input.split(',') if k.strip()]

        # 固定使用DeepSeek R1
        ai_provider = 'deepseek'
        print(f"\n🤖 使用AI提供商: DeepSeek R1")

        # 执行AI分类
        result = ai_classify_limit_up_stocks(query_date, keywords, ai_provider=ai_provider)

        if result['success']:
            export_path = export_ai_classification(result)
            print(f"\n✅ AI智能分类完成")
            if export_path:
                print(f"📄 结果文件: {export_path}")

            # 显示使用提示
            print(f"\n💡 提示:")
            print(f"   1. 请确保已设置API密钥环境变量")
            print(f"   2. DeepSeek: DEEPSEEK_API_KEY")
            print(f"   3. Kimi: KIMI_API_KEY")
        else:
            print(f"\n❌ AI分类失败: {result.get('error', '未知错误')}")
            if 'API' in result.get('error', ''):
                print("💡 请检查API密钥设置和网络连接")

if __name__ == "__main__":
    main()
