# 导入所需的库
import akshare as ak
import pandas as pd
from datetime import datetime, date

# 设置目标年月
target_year = 2025
target_months = [5, 6]  # 可以添加多个月份

def get_trade_dates():
    """获取交易日历数据"""
    try:
        # 获取新浪交易日历数据
        trade_dates_df = ak.tool_trade_date_hist_sina()
        
        # 转换日期格式
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def filter_dates(df, year, months):
    """筛选指定年月的交易日期"""
    if df.empty:
        return pd.DataFrame()
    
    # 星期几的英文到中文映射
    weekday_map = {
        'Monday': '星期一',
        'Tuesday': '星期二',
        'Wednesday': '星期三',
        'Thursday': '星期四',
        'Friday': '星期五',
        'Saturday': '星期六',
        'Sunday': '星期日'
    }
    
    # 筛选指定年月的数据
    mask = (df['trade_date'].dt.year == year) & (df['trade_date'].dt.month.isin(months))
    filtered_df = df[mask].copy()
    
    # 添加年月列和星期几列
    filtered_df['year_month'] = filtered_df['trade_date'].dt.strftime('%Y-%m')
    filtered_df['weekday'] = filtered_df['trade_date'].dt.day_name().map(weekday_map)
    
    # 重新排序列
    filtered_df = filtered_df[['trade_date', 'year_month', 'weekday']]
    
    # 重命名列
    filtered_df.columns = ['交易日期', '年月', '星期']
    
    return filtered_df

def export_to_excel(df, year, months):
    """导出数据到Excel"""
    if df.empty:
        print("没有数据可导出")
        return
    
    # 设置输出文件名
    months_str = '_'.join(f"{month:02d}" for month in months)
    filename = f"trade_dates_{year}_{months_str}.xlsx"
    
    try:
        # 创建Excel写入器，设置多个sheet
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出总表
            df_all = df.copy()
            df_all['交易日期'] = df_all['交易日期'].dt.strftime('%Y-%m-%d')
            df_all.to_excel(writer, sheet_name='全部日期', index=False)
            
            # 按月份分别导出
            for month in months:
                month_mask = df['交易日期'].dt.month == month
                df_month = df[month_mask].copy()
                df_month['交易日期'] = df_month['交易日期'].dt.strftime('%Y-%m-%d')
                sheet_name = f"{month}月"
                df_month.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"数据已成功导出到 {filename}")
    except Exception as e:
        print(f"导出数据时出错: {str(e)}")

def print_monthly_summary(df):
    """打印每月交易日统计"""
    monthly_counts = df.groupby('年月').size()
    print("\n每月交易日统计:")
    for month, count in monthly_counts.items():
        print(f"{month}: {count}个交易日")
    print(f"\n总计: {len(df)}个交易日\n")

def main():
    # 获取交易日历数据
    print("正在获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    
    if not trade_dates_df.empty:
        # 筛选指定年月的数据
        months_str = '、'.join(str(m) for m in target_months)
        print(f"正在筛选 {target_year}年 {months_str}月 的数据...")
        filtered_df = filter_dates(trade_dates_df, target_year, target_months)
        
        if len(filtered_df) > 0:
            # 打印预览
            print("\n数据预览:")
            print(filtered_df.head())
            
            # 打印月度统计
            print_monthly_summary(filtered_df)
            
            # 导出到Excel
            export_to_excel(filtered_df, target_year, target_months)
        else:
            print(f"未找到 {target_year}年 {months_str}月 的交易日期数据")
    else:
        print("获取交易日历数据失败")

if __name__ == "__main__":
    main() 