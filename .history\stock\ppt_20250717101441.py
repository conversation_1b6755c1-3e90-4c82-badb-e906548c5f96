import os
from pptx import Presentation
from datetime import datetime

def export_ppt_notes(ppt_path, output_dir=None):
    """
    导出PPT中所有幻灯片的备注
    
    Args:
        ppt_path (str): PPT文件的路径
        output_dir (str, optional): 输出目录，默认为PPT所在目录
    
    Returns:
        str: 输出文件的路径
    """
    try:
        # 检查PPT文件是否存在
        if not os.path.exists(ppt_path):
            raise FileNotFoundError(f"找不到PPT文件: {ppt_path}")
            
        # 获取输出目录
        if output_dir is None:
            output_dir = os.path.dirname(ppt_path)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        ppt_name = os.path.splitext(os.path.basename(ppt_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"{ppt_name}_notes_{timestamp}.txt")
        
        # 打开PPT文件
        prs = Presentation(ppt_path)
        
        # 创建输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"PPT文件名: {ppt_name}\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("-" * 50 + "\n\n")
            
            # 遍历所有幻灯片
            for slide_number, slide in enumerate(prs.slides, 1):
                # 获取备注
                notes = slide.notes_slide
                if notes and notes.notes_text_frame.text.strip():
                    f.write(f"第 {slide_number} 张幻灯片的备注:\n")
                    f.write(notes.notes_text_frame.text.strip() + "\n")
                    f.write("-" * 30 + "\n\n")
        
        print(f"备注已导出到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"导出过程中出现错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 使用示例
    ppt_path = input("请输入PPT文件的路径: ").strip('"')  # 去除可能的引号
    export_ppt_notes(ppt_path)
