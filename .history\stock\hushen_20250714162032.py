import pywencai
import pandas as pd
from datetime import datetime, timed<PERSON>ta


def get_market_data(date=None):
    """获取沪深股市场数据
    包括：成交量、涨停家数、跌停家数、上涨家数、下跌家数
    """
    try:
        # 如果没有指定日期，使用最近交易日
        if not date:
            date = datetime.now().strftime('%Y%m%d')
            
        # 构建问财查询语句 - 使用883421指数作为沪深股范围
        query = f"883421指数{date}的成交量，涨跌停情况，涨跌家数"
        
        # 获取数据
        df = pywencai.get(query=query, loop=True)
        
        if isinstance(df, pd.DataFrame) and not df.empty:
            print("\n原始数据列：", df.columns.tolist())
            
            # 提取数据（根据实际返回的列名调整）
            result = {
                '日期': date,
                '成交量(亿)': float(df.iloc[0]['成交额']) / 100000000 if '成交额' in df.columns else 0,
                '涨停家数': int(df.iloc[0]['涨停家数']) if '涨停家数' in df.columns else 0,
                '跌停家数': int(df.iloc[0]['跌停家数']) if '跌停家数' in df.columns else 0,
                '上涨家数': int(df.iloc[0]['上涨家数']) if '上涨家数' in df.columns else 0,
                '下跌家数': int(df.iloc[0]['下跌家数']) if '下跌家数' in df.columns else 0
            }
            
            print(f"\n{date}市场数据:")
            print(f"成交量: {result['成交量(亿)']:.2f}亿")
            print(f"涨停/跌停: {result['涨停家数']}/{result['跌停家数']}")
            print(f"上涨/下跌: {result['上涨家数']}/{result['下跌家数']}")
            
            return result
        else:
            print(f"未获取到{date}的市场数据")
            return None
            
    except Exception as e:
        print(f"获取市场数据失败: {str(e)}")
        return None


def get_recent_market_data(days=10):
    """获取最近n天的市场数据"""
    try:
        # 构建多日查询语句
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days-1)).strftime('%Y%m%d')
        
        query = f"883421指数{start_date}至{end_date}的日期，成交量，涨跌停情况，涨跌家数"
        df = pywencai.get(query=query, loop=True)
        
        if isinstance(df, pd.DataFrame) and not df.empty:
            print("\n原始数据列：", df.columns.tolist())
            
            # 处理数据
            result_df = pd.DataFrame({
                '日期': df['日期'],
                '成交量(亿)': df['成交额'].astype(float) / 100000000,
                '涨停家数': df['涨停家数'].astype(int),
                '跌停家数': df['跌停家数'].astype(int),
                '上涨家数': df['上涨家数'].astype(int),
                '下跌家数': df['下跌家数'].astype(int)
            })
            
            # 保存到Excel
            result_df.to_excel('hushen_market_data.xlsx', index=False)
            print('\n数据已保存到 hushen_market_data.xlsx')
            
            # 打印最新一天的数据
            latest = result_df.iloc[-1]
            print(f"\n最新市场数据 ({latest['日期']}):")
            print(f"成交量: {latest['成交量(亿)']:.2f}亿")
            print(f"涨停/跌停: {latest['涨停家数']}/{latest['跌停家数']}")
            print(f"上涨/下跌: {latest['上涨家数']}/{latest['下跌家数']}")
            
            return result_df
            
    except Exception as e:
        print(f"获取市场数据失败: {str(e)}")
    
    return None


if __name__ == "__main__":
    # 获取最近10天的数据
    get_recent_market_data(10)
