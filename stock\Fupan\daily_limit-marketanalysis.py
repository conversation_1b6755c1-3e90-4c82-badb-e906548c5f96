import akshare as ak
import pandas as pd
import pywencai
import os
import sys
import time
from datetime import datetime, date, timedelta
from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

class RateLimitManager:
    """频率限制管理器，用于控制API调用频率"""

    def __init__(self, max_requests_per_minute=1, enabled=False):
        """
        初始化频率限制管理器

        Args:
            max_requests_per_minute (int): 每分钟最多请求次数，默认1次
            enabled (bool): 是否启用频率限制，默认False
        """
        self.max_requests_per_minute = max_requests_per_minute
        self.enabled = enabled
        self.request_times = []  # 存储请求时间戳
        self.call_count = 0

    def wait_if_needed(self, show_progress=True):
        """如果需要，等待以满足频率限制"""
        if not self.enabled:
            return

        current_time = time.time()

        # 清理超过1分钟的旧记录
        one_minute_ago = current_time - 60
        self.request_times = [t for t in self.request_times if t > one_minute_ago]

        # 检查是否超过频率限制
        if len(self.request_times) >= self.max_requests_per_minute:
            # 计算需要等待的时间
            oldest_request = min(self.request_times)
            wait_time = 60 - (current_time - oldest_request)

            if wait_time > 0:
                if show_progress:
                    print(f"⏰ 频率限制: 每分钟最多{self.max_requests_per_minute}次请求，等待 {wait_time:.1f} 秒...")
                    # 显示倒计时
                    for remaining in range(int(wait_time), 0, -1):
                        print(f"\r   ⏳ 倒计时: {remaining} 秒", end='', flush=True)
                        time.sleep(1)
                    print(f"\r   ✅ 等待完成，继续执行...     ")
                else:
                    time.sleep(wait_time)

        # 记录本次请求时间
        self.request_times.append(current_time)
        self.call_count += 1

        if show_progress:
            remaining_quota = self.max_requests_per_minute - len(self.request_times)
            print(f"🔄 第 {self.call_count} 次请求 (本分钟剩余配额: {remaining_quota})")

    def reset(self):
        """重置频率限制管理器"""
        self.request_times = []
        self.call_count = 0

    def set_rate_limit(self, max_requests_per_minute):
        """设置频率限制"""
        self.max_requests_per_minute = max_requests_per_minute

    def get_current_status(self):
        """获取当前状态"""
        current_time = time.time()
        one_minute_ago = current_time - 60
        recent_requests = [t for t in self.request_times if t > one_minute_ago]

        return {
            'enabled': self.enabled,
            'max_per_minute': self.max_requests_per_minute,
            'recent_requests': len(recent_requests),
            'remaining_quota': self.max_requests_per_minute - len(recent_requests),
            'total_calls': self.call_count
        }



# 全局频率限制管理器实例
rate_limiter = RateLimitManager()

def show_rate_limit_info():
    """显示频率限制功能说明"""
    print("\n💡 频率限制功能说明:")
    print("   - 用于避免频繁调用wencai接口被拒绝")
    print("   - 建议在获取多个交易日数据时启用")
    print("   - 可自定义每分钟最多获取的交易日数量")
    print("   - 默认每分钟最多1个交易日（安全模式）")
    print("   - 可设置为2-5个/分钟（根据网络情况调整）")
    print("   - 会显示剩余配额和倒计时进度")
    print("   - 自动管理请求间隔，无需手动等待")

def safe_len(df):
    """安全获取DataFrame长度"""
    return len(df) if df is not None and not df.empty else 0

def handle_result(result, success_msg, fail_msg, export_func=None):
    """统一处理结果输出"""
    if result['success']:
        if export_func:
            export_func(result)
        print(f"\n✅ {success_msg}")
    else:
        print(f"\n❌ {fail_msg}")
        return False
    return True

def create_output_directory():
    """创建输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(script_dir, "market_analysis_output")

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    return output_dir

def get_default_excel_path():
    """获取默认的Excel文件路径"""
    default_folder = r"C:\Users\<USER>\Nutstore\1\我的坚果云"
    default_filename = "复盘.xlsx"
    default_file_path = os.path.join(default_folder, default_filename)

    return default_file_path, default_folder

def get_output_file_choice():
    """获取用户的输出文件选择"""
    print("\n📁 输出文件选择:")
    print("1. 使用默认路径 (推荐)")
    print("2. 自定义路径")

    while True:
        choice = input("请选择输出方式 (1/2): ").strip()
        if choice in ['1', '2']:
            return choice
        print("❌ 请输入 1 或 2")

def get_excel_output_path():
    """获取Excel输出路径"""
    choice = get_output_file_choice()

    if choice == '1':
        # 使用默认路径
        default_path, default_folder = get_default_excel_path()
        print(f"📁 默认Excel文件: {default_path}")

        # 检查默认文件夹是否存在
        if os.path.exists(default_folder):
            print("✅ 默认路径可用")
            return default_path
        else:
            print(f"❌ 默认路径不存在: {default_folder}")
            print("🔄 切换到自定义路径模式...")
            choice = '2'

    if choice == '2':
        # 自定义路径
        while True:
            custom_path = input("请输入Excel文件完整路径 (包含文件名): ").strip()
            if not custom_path:
                print("❌ 路径不能为空")
                continue

            # 确保文件扩展名为.xlsx
            if not custom_path.lower().endswith('.xlsx'):
                custom_path += '.xlsx'

            # 检查目录是否存在
            custom_dir = os.path.dirname(custom_path)
            if custom_dir and not os.path.exists(custom_dir):
                create_dir = input(f"目录 {custom_dir} 不存在，是否创建? (y/n): ").strip().lower()
                if create_dir in ['y', 'yes']:
                    try:
                        os.makedirs(custom_dir, exist_ok=True)
                        print(f"✅ 目录已创建: {custom_dir}")
                    except Exception as e:
                        print(f"❌ 创建目录失败: {str(e)}")
                        continue
                else:
                    continue

            print(f"✅ 输出路径: {custom_path}")
            return custom_path

def get_sheet_name_choice():
    """获取工作表名称选择"""
    print("\n📋 工作表名称选择:")
    print("1. 使用默认名称 'total' (推荐)")
    print("2. 自定义名称")

    while True:
        choice = input("请选择工作表名称 (1/2): ").strip()
        if choice == '1':
            return 'total'
        elif choice == '2':
            while True:
                custom_name = input("请输入工作表名称: ").strip()
                if custom_name:
                    return custom_name
                print("❌ 工作表名称不能为空")
        else:
            print("❌ 请输入 1 或 2")

def get_trade_dates():
    """获取交易日历数据"""
    try:
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def get_trading_dates_in_range(start_date, end_date):
    """获取指定日期范围内的交易日"""
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        return []

    # 转换为date对象进行比较
    if isinstance(start_date, str):
        if len(start_date) == 8:  # YYYYMMDD
            start_date = datetime.strptime(start_date, '%Y%m%d').date()
        else:  # YYYY-MM-DD
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    if isinstance(end_date, str):
        if len(end_date) == 8:  # YYYYMMDD
            end_date = datetime.strptime(end_date, '%Y%m%d').date()
        else:  # YYYY-MM-DD
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    filtered_dates = [d for d in trading_dates if start_date <= d <= end_date]

    return sorted(filtered_dates)

def get_excel_sheets(excel_file_path):
    """获取Excel文件中的所有sheet名称"""
    try:
        if not os.path.exists(excel_file_path):
            return None, "文件不存在"

        # 获取所有sheet名称
        excel_file = pd.ExcelFile(excel_file_path)
        sheet_names = excel_file.sheet_names
        excel_file.close()

        return sheet_names, None
    except Exception as e:
        return None, f"读取Excel文件失败: {str(e)}"

def read_existing_excel_data(excel_file_path, sheet_name='total'):
    """读取Excel文件指定sheet并获取最新日期（支持列格式数据）"""
    try:
        if not os.path.exists(excel_file_path):
            return None, None, "文件不存在"

        # 读取指定sheet，不设置index_col，因为我们使用列格式
        df = pd.read_excel(excel_file_path, sheet_name=sheet_name, header=None)

        if df.empty:
            return None, None, "Excel文件为空"

        # 检查是否有日期行（第一行）
        if len(df) < 2:
            return None, None, "Excel文件数据不足"

        # 第一行应该是日期行，第一列是"日期"标签
        first_row = df.iloc[0].tolist()

        if first_row[0] != '日期':
            # 如果第一行不是日期行，尝试旧格式（行格式）
            df_old_format = pd.read_excel(excel_file_path, sheet_name=sheet_name, index_col=0)
            if not df_old_format.empty:
                # 转换旧格式到新格式
                indicators = df_old_format.index.tolist()
                date_columns = df_old_format.columns.tolist()

                # 创建新格式的DataFrame
                new_data = [['日期'] + date_columns]
                for indicator in indicators:
                    row_data = [indicator]
                    for date_col in date_columns:
                        row_data.append(df_old_format.loc[indicator, date_col])
                    new_data.append(row_data)

                df = pd.DataFrame(new_data)
                first_row = df.iloc[0].tolist()

        # 获取日期列（除了第一列的"日期"标签）
        date_columns = first_row[1:]  # 跳过第一列的"日期"标签

        # 转换为date对象并排序
        date_objects = []
        for date_str in date_columns:
            try:
                if isinstance(date_str, str):
                    if len(date_str) == 10 and '-' in date_str:  # YYYY-MM-DD
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    elif len(date_str) == 8:  # YYYYMMDD
                        date_obj = datetime.strptime(date_str, '%Y%m%d').date()
                    else:
                        continue
                else:
                    continue
                date_objects.append(date_obj)
            except:
                continue

        if not date_objects:
            return None, None, "无法解析日期"

        # 获取最新日期
        latest_date = max(date_objects)

        # 重新构建DataFrame，确保格式正确
        # 第一行是日期行，后续行是指标和数据
        headers = ['指标'] + [col for col in first_row[1:]]  # 重新构建列名
        df.columns = range(len(df.columns))  # 重置列索引

        # 创建最终的DataFrame
        final_data = []
        for i in range(1, len(df)):  # 跳过第一行（日期行）
            row_data = df.iloc[i].tolist()
            if len(row_data) >= len(headers):
                final_data.append(row_data[:len(headers)])

        if final_data:
            final_df = pd.DataFrame(final_data, columns=headers)
        else:
            final_df = pd.DataFrame(columns=headers)

        return final_df, latest_date, None

    except Exception as e:
        return None, None, f"读取Excel文件失败: {str(e)}"

def get_missing_trading_dates(latest_date, end_date=None):
    """获取缺失的交易日"""
    if end_date is None:
        end_date = date.today()

    # 如果end_date是字符串，转换为date对象
    if isinstance(end_date, str):
        if len(end_date) == 8:  # YYYYMMDD
            end_date = datetime.strptime(end_date, '%Y%m%d').date()
        else:  # YYYY-MM-DD
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # 获取从latest_date的下一个交易日到end_date的所有交易日
    next_day = latest_date + timedelta(days=1)
    trading_dates = get_trading_dates_in_range(next_day, end_date)

    return trading_dates

def get_market_data(date, query_type):
    """获取市场数据"""
    query_map = {
        'limit_up': f"非ST,沪深主板,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,沪深主板,{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,沪深主板,{date.strftime('%Y%m%d')}曾涨停"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            loop=True
        )
        if df is None:
            print(f"⚠️ {query_type}查询返回空结果")
            return None
        if df.empty:
            print(f"⚠️ {query_type}查询结果为空")
            return None
        return df
    except Exception as e:
        print(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_concept_counts(df, date, min_count=2):
    """统计涨停概念"""
    if df is None or df.empty:
        return pd.DataFrame()

    reason_col = f'涨停原因类别[{date.strftime("%Y%m%d")}]'
    if reason_col not in df.columns:
        possible_cols = [col for col in df.columns if '涨停原因' in col or '概念' in col]
        if possible_cols:
            reason_col = possible_cols[0]
        else:
            return pd.DataFrame()

    try:
        concepts_series = df[reason_col].dropna().astype(str)
        concepts_series = concepts_series[~concepts_series.isin(['nan', 'None', '', '无'])]

        if concepts_series.empty:
            return pd.DataFrame()

        concepts = concepts_series.str.split('+').explode().reset_index(drop=True)
        concepts = concepts.str.strip()
        concepts = concepts[concepts != '']

        if concepts.empty:
            return pd.DataFrame()

        concept_counts = concepts.value_counts()
        concept_counts = concept_counts[concept_counts >= min_count]

        if concept_counts.empty:
            return pd.DataFrame()

        concept_stats = [
            {'指标': f'热门概念{i+1}', '数值': f"{concept}({count})"}
            for i, (concept, count) in enumerate(concept_counts.head(10).items())
        ]

        return pd.DataFrame(concept_stats)

    except Exception as e:
        print(f"统计概念时出错: {str(e)}")
        return pd.DataFrame()

def query_industry_capital_flow(query_date):
    """查询同花顺行业资金流向数据"""
    try:
        if isinstance(query_date, str):
            date_obj = datetime.strptime(query_date, '%Y%m%d').date()
        else:
            date_obj = query_date

        print(f"🔍 查询 {date_obj.strftime('%Y%m%d')} 行业资金流向...")

        # 直接执行查询
        query = f"{date_obj.strftime('%Y%m%d')} 同花顺行业指数 资金流向"

        try:
            df = pywencai.get(query=query, query_type='zhishu', loop=True)
        except Exception as e:
            print(f"❌ 行业资金流向查询失败: {str(e)}")
            return [], []

        if df is None or df.empty:
            print(f"⚠️ 行业资金流向查询无结果")
            return [], []

        # 列名匹配
        name_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in
                    ['指数简称', '简称', '名称', '指数名称', 'name', '行业'])]
        flow_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in
                    ['资金流向', '净流入', '资金流', '流入', '流出', 'flow', '资金'])]

        if not name_cols or not flow_cols:
            return [], []

        result_df = df[[name_cols[0], flow_cols[0]]].copy()
        result_df.columns = ['指数简称', '资金流向']
        result_df['资金流向'] = pd.to_numeric(result_df['资金流向'], errors='coerce')
        result_df = result_df.dropna()

        if result_df.empty:
            return [], []

        # 分离流入和流出数据
        inflow_df = result_df[result_df['资金流向'] > 0].sort_values('资金流向', ascending=False).head(3)
        outflow_df = result_df[result_df['资金流向'] < 0].sort_values('资金流向', ascending=True).head(3)

        inflow_result = [(row['指数简称'], row['资金流向']) for _, row in inflow_df.iterrows()]
        outflow_result = [(row['指数简称'], row['资金流向']) for _, row in outflow_df.iterrows()]

        return inflow_result, outflow_result

    except Exception as e:
        print(f"❌ 查询行业资金流向失败: {str(e)}")
        return [], []

def query_concept_index_performance(query_date):
    """查询同花顺概念指数涨跌幅数据"""
    try:
        if isinstance(query_date, str):
            date_obj = datetime.strptime(query_date, '%Y%m%d').date()
        else:
            date_obj = query_date

        print(f"🔍 查询 {date_obj.strftime('%Y%m%d')} 概念指数涨跌幅...")

        # 直接执行查询
        date_str = date_obj.strftime('%Y%m%d')
        query = f"同花顺概念指数 {date_str} 涨跌幅"

        try:
            df = pywencai.get(query=query, query_type='zhishu', loop=True)
        except Exception as e:
            print(f"❌ 概念指数查询失败: {str(e)}")
            return [], []

        if df is None or df.empty:
            print(f"⚠️ 概念指数查询无结果")
            return [], []

        # 列名匹配 - 优先匹配包含日期的列名
        date_str = date_obj.strftime('%Y%m%d')

        # 查找指数名称列
        name_cols = [col for col in df.columns if any(keyword in col.lower() for keyword in
                    ['指数简称', '简称', '名称', '指数名称', 'name', '概念'])]

        # 处理问财返回的多日期数据 - 查找指定日期的涨跌幅列
        print(f"🔍 可用列名: {list(df.columns)}")

        # 将日期转换为可能的格式
        target_date_formats = [
            date_obj.strftime('%Y.%m.%d'),    # 2025.04.01
            date_obj.strftime('%Y-%m-%d'),    # 2025-04-01
            date_obj.strftime('%Y/%m/%d'),    # 2025/04/01
            date_obj.strftime('%m.%d'),       # 04.01
            date_obj.strftime('%m-%d'),       # 04-01
            date_obj.strftime('%m/%d'),       # 04/01
            date_str,                         # 20250401
        ]

        # 查找包含指定日期的涨跌幅列
        target_change_col = None
        for col in df.columns:
            col_str = str(col)
            # 检查列名是否包含涨跌幅相关关键词
            if any(keyword in col_str.lower() for keyword in ['涨跌幅', '涨幅', '跌幅', '涨跌', 'change', '变动']):
                # 检查是否包含目标日期
                for date_format in target_date_formats:
                    if date_format in col_str:
                        target_change_col = col
                        print(f"✅ 找到目标日期的涨跌幅列: {col} (匹配格式: {date_format})")
                        break
                if target_change_col:
                    break

        # 如果没有找到包含目标日期的列，尝试通过列的位置或内容来判断
        if not target_change_col:
            print(f"⚠️ 未找到包含目标日期的列，尝试其他方法...")

            # 获取所有涨跌幅相关的列
            all_change_cols = [col for col in df.columns if any(keyword in str(col).lower()
                              for keyword in ['涨跌幅', '涨幅', '跌幅', '涨跌', 'change', '变动'])]

            if all_change_cols:
                print(f"📋 所有涨跌幅列: {all_change_cols}")

                # 如果有多个涨跌幅列，尝试找到不是最新日期的那一列
                target_month = date_obj.month

                best_col = None
                for col in all_change_cols:
                    col_str = str(col)
                    # 如果列名包含目标月份，优先选择
                    if f".{target_month:02d}." in col_str or f"/{target_month:02d}/" in col_str or f"-{target_month:02d}-" in col_str:
                        best_col = col
                        break

                target_change_col = best_col if best_col else all_change_cols[0]
                print(f"🎯 选择涨跌幅列: {target_change_col}")

        if not name_cols or not target_change_col:
            print(f"❌ 未找到合适的列名")
            print(f"   指数名称列: {name_cols}")
            print(f"   目标涨跌幅列: {target_change_col}")
            return [], []

        print(f"📊 使用列名 - 指数名称: {name_cols[0]}, 涨跌幅: {target_change_col}")
        change_cols = [target_change_col]
        result_df = df[[name_cols[0], change_cols[0]]].copy()
        result_df.columns = ['指数简称', '涨跌幅']
        result_df['涨跌幅'] = pd.to_numeric(result_df['涨跌幅'], errors='coerce')
        result_df = result_df.dropna()

        if result_df.empty:
            return [], []

        # 分离涨幅和跌幅
        rise_df = result_df[result_df['涨跌幅'] > 0].sort_values('涨跌幅', ascending=False).head(10)
        fall_df = result_df[result_df['涨跌幅'] < 0].sort_values('涨跌幅', ascending=True).head(10)

        rise_result = [(row['指数简称'], row['涨跌幅']) for _, row in rise_df.iterrows()]
        fall_result = [(row['指数简称'], row['涨跌幅']) for _, row in fall_df.iterrows()]

        return rise_result, fall_result

    except Exception as e:
        print(f"❌ 查询概念指数失败: {str(e)}")
        return [], []

def query_market_overview(query_date):
    """查询市场概览数据（成交额、上涨下跌家数）"""
    try:
        if isinstance(query_date, str):
            date_obj = datetime.strptime(query_date, '%Y%m%d').date()
        else:
            date_obj = query_date

        print(f"🔍 查询 {date_obj.strftime('%Y%m%d')} 市场概览...")

        query = f"{date_obj.strftime('%Y%m%d')} 同花顺全A(沪深) 成交额 上涨家数 下跌家数"
        df = pywencai.get(query=query, query_type='zhishu', loop=True)

        if df is None:
            print(f"⚠️ 市场概览查询返回空结果")
            return {}
        if df.empty:
            print(f"⚠️ 市场概览查询结果为空")
            return {}

        result = {}
        for col in df.columns:
            if '成交额' in col:
                value = df[col].iloc[0] if len(df) > 0 else 0
                result['成交额'] = pd.to_numeric(value, errors='coerce')
            elif '上涨家数' in col:
                value = df[col].iloc[0] if len(df) > 0 else 0
                result['上涨家数'] = pd.to_numeric(value, errors='coerce')
            elif '下跌家数' in col:
                value = df[col].iloc[0] if len(df) > 0 else 0
                result['下跌家数'] = pd.to_numeric(value, errors='coerce')
            elif '平盘家数' in col:
                value = df[col].iloc[0] if len(df) > 0 else 0
                result['平盘家数'] = pd.to_numeric(value, errors='coerce')

        return result

    except Exception as e:
        print(f"❌ 查询市场概览失败: {str(e)}")
        return {}

def create_ordered_summary(market_stats, promotion_rates, concept_counts, inflow_data, outflow_data, concept_rise_data, concept_fall_data):
    """按照控制台打印顺序创建有序的汇总数据"""
    ordered_stats = []

    # 1. 📋 关键指标 (market_stats)
    if not market_stats.empty:
        ordered_stats.extend(market_stats.to_dict('records'))

    # 2. 📊 连板晋级率分析 (promotion_rates)
    if not promotion_rates.empty:
        ordered_stats.extend(promotion_rates.to_dict('records'))

    # 3. 🔥 热门概念 (concept_counts)
    if not concept_counts.empty:
        ordered_stats.extend(concept_counts.to_dict('records'))

    # 4. 💰 行业资金流向
    if inflow_data:
        for i, (name, amount) in enumerate(inflow_data, 1):
            ordered_stats.append({
                '指标': f'资金流入{i}(亿元)',
                '数值': f"{name}({amount/100000000:.0f})"
            })

    if outflow_data:
        for i, (name, amount) in enumerate(outflow_data, 1):
            ordered_stats.append({
                '指标': f'资金流出{i}(亿元)',
                '数值': f"{name}({amount/100000000:.0f})"
            })

    # 5. 📈 概念指数表现
    if concept_rise_data:
        for i, (name, change) in enumerate(concept_rise_data, 1):
            ordered_stats.append({
                '指标': f'概念涨幅{i}(%)',
                '数值': f"{name}(+{change:.1f})"
            })

    if concept_fall_data:
        for i, (name, change) in enumerate(concept_fall_data, 1):
            ordered_stats.append({
                '指标': f'概念跌幅{i}(%)',
                '数值': f"{name}({change:.1f})"
            })

    return pd.DataFrame(ordered_stats)

def get_indicator_order():
    """定义指标的显示顺序"""
    base_order = [
        '日期', '成交额(亿元)', '上涨家数', '下跌家数',
        '涨停家数', '跌停家数', '最高板数', '破板家数', '破板率',
        '昨日涨停收益', '今日接板收益'
    ]

    # 重新组织指标顺序
    ordered_indicators = base_order.copy()

    # 添加1-6板数量
    for i in range(1, 7):
        ordered_indicators.append(f'{i}板数量')

    # 添加6板以上数量（紧跟6板数量）
    ordered_indicators.append('6板以上数量')

    # 添加7-10板数量
    for i in range(7, 11):
        ordered_indicators.append(f'{i}板数量')

    # 添加总晋级率和各板晋级率
    ordered_indicators.append('总晋级率')
    for i in range(1, 10):
        ordered_indicators.append(f'{i}进{i+1}晋级率')

    # 添加热门概念
    for i in range(1, 11):
        ordered_indicators.append(f'热门概念{i}')

    # 添加资金流向
    for i in range(1, 4):
        ordered_indicators.append(f'资金流入{i}(亿元)')
    for i in range(1, 4):
        ordered_indicators.append(f'资金流出{i}(亿元)')

    # 添加概念指数
    for i in range(1, 11):
        ordered_indicators.append(f'概念涨幅{i}(%)')
    for i in range(1, 11):
        ordered_indicators.append(f'概念跌幅{i}(%)')

    return ordered_indicators

def sort_indicators_by_order(indicators):
    """按照定义的顺序对指标进行排序"""
    order_list = get_indicator_order()
    order_dict = {indicator: i for i, indicator in enumerate(order_list)}

    # 对指标进行排序，未在order_list中的指标放在最后
    def sort_key(indicator):
        return order_dict.get(indicator, len(order_list))

    return sorted(indicators, key=sort_key)


def get_indicator_category(indicator):
    """获取指标类别"""
    if indicator in ['日期', '成交额(亿元)', '上涨家数', '下跌家数',
                     '涨停家数', '跌停家数', '最高板数', '破板家数', '破板率',
                     '昨日涨停收益', '今日接板收益', '6板以上数量'] or indicator.endswith('板数量'):
        return 'market'  # 关键指标
    elif '晋级率' in indicator:
        return 'promotion'  # 晋级率统计
    elif '热门概念' in indicator:
        return 'hot_concept'  # 热门概念
    elif '资金流入' in indicator or '资金流出' in indicator:
        return 'capital'  # 资金流向
    elif '概念涨幅' in indicator or '概念跌幅' in indicator:
        return 'concept'  # 概念指数
    else:
        return 'other'  # 其他

def apply_excel_formatting_vertical(worksheet, df):
    try:
        # 定义指标列的颜色方案 - 40%着色（更明显的颜色）
        category_colors = {
            'market': 'FFD1E7F7',      # 40%蓝色 - 关键指标
            'promotion': 'FFD1EBD1',   # 40%绿色 - 晋级率统计
            'hot_concept': 'FFF7E5D1', # 40%橙色 - 热门概念
            'capital': 'FFE0E0E0',     # 40%灰色 - 资金流向
            'concept': 'FFFFF9D1',     # 40%黄色 - 概念指数
            'other': 'FFDDDDDD'        # 40%浅灰色 - 其他
        }

        # 基本样式
        data_font = Font(size=10)
        center_alignment = Alignment(horizontal='center', vertical='center')

        thin_border = Border(
            left=Side(style='thin', color='FFD0D0D0'),
            right=Side(style='thin', color='FFD0D0D0'),
            top=Side(style='thin', color='FFD0D0D0'),
            bottom=Side(style='thin', color='FFD0D0D0')
        )

        # 设置第一行（日期行）的特殊格式
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            if col == 1:
                # 第一个单元格（"日期"标签）
                cell.fill = PatternFill(start_color='FF4472C4', end_color='FF4472C4', fill_type='solid')
                cell.font = Font(color='FFFFFFFF', bold=True, size=11)
            else:
                # 日期单元格
                cell.fill = PatternFill(start_color='FFE7F3FF', end_color='FFE7F3FF', fill_type='solid')
                cell.font = Font(color='FF000000', bold=True, size=10)
            cell.alignment = center_alignment
            cell.border = thin_border

        # 设置指标列（第一列，从第二行开始）的颜色标注
        for row in range(2, len(df) + 1):
            indicator = df.iloc[row-1, 0]
            category = get_indicator_category(indicator)

            cell = worksheet.cell(row=row, column=1)
            cell.fill = PatternFill(start_color=category_colors[category],
                                  end_color=category_colors[category], fill_type='solid')
            cell.font = Font(color='FF000000', bold=True, size=10)
            cell.alignment = center_alignment
            cell.border = thin_border

        # 设置数据列样式（从第二行开始）
        for row in range(2, len(df) + 1):
            for col in range(2, len(df.columns) + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

        # 冻结第一行和第一列
        worksheet.freeze_panes = 'B2'

        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max(max_length + 2, 12), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    except Exception as e:
        print(f"⚠️ Excel格式化失败: {str(e)}")

def get_yesterday_limit_up_performance_with_days(selected_date, previous_date):
    """计算昨日涨停股票的收益指标，同时返回连板数据用于晋级率计算"""
    if previous_date is None:
        return 0.0, 0.0, pd.DataFrame()

    try:
        # 使用合并查询方式（同时获取收益数据和连板数据）
        yesterday_str = previous_date.strftime('%Y%m%d')
        today_str = selected_date.strftime('%Y%m%d')
        query = f"{yesterday_str}涨停 价格,沪深主板,非ST,{today_str}涨跌幅 价格"

        print(f"🔍 查询昨日涨停收益和连板数据: {query}")

        df = pywencai.get(query=query, loop=True)

        if df is None or df.empty:
            print("❌ 未获取到昨日涨停价格数据")
            return 0.0, 0.0

        print(f"✅ 获取到{len(df)}只昨日涨停股票的价格数据")

        # 显示所有列名用于调试
        print(f"   📋 所有列名: {list(df.columns)}")

        # 查找相关列 - 使用多种可能的日期格式
        yesterday_formats = [
            f"[{previous_date.strftime('%Y%m%d')}]",  # [20250401]
            f"[{previous_date.strftime('%Y.%m.%d')}]",  # [2025.04.01]
            f"[{previous_date.strftime('%Y-%m-%d')}]",  # [2025-04-01]
            previous_date.strftime('%Y%m%d'),  # 20250401
            previous_date.strftime('%Y.%m.%d'),  # 2025.04.01
            previous_date.strftime('%Y-%m-%d'),  # 2025-04-01
        ]

        today_formats = [
            f"[{selected_date.strftime('%Y%m%d')}]",  # [20250402]
            f"[{selected_date.strftime('%Y.%m.%d')}]",  # [2025.04.02]
            f"[{selected_date.strftime('%Y-%m-%d')}]",  # [2025-04-02]
            selected_date.strftime('%Y%m%d'),  # 20250402
            selected_date.strftime('%Y.%m.%d'),  # 2025.04.02
            selected_date.strftime('%Y-%m-%d'),  # 2025-04-02
        ]

        # 查找昨日收盘价列
        yesterday_close_cols = []
        for date_format in yesterday_formats:
            cols = [col for col in df.columns if '收盘价' in col and date_format in col]
            if cols:
                yesterday_close_cols = cols
                print(f"   ✅ 找到昨日收盘价列 (格式: {date_format}): {cols}")
                break

        # 查找今日收盘价列
        today_close_cols = []
        for date_format in today_formats:
            cols = [col for col in df.columns if '收盘价' in col and date_format in col]
            if cols:
                today_close_cols = cols
                print(f"   ✅ 找到今日收盘价列 (格式: {date_format}): {cols}")
                break

        # 查找今日开盘价列
        today_open_cols = []
        for date_format in today_formats:
            cols = [col for col in df.columns if '开盘价' in col and date_format in col]
            if cols:
                today_open_cols = cols
                print(f"   ✅ 找到今日开盘价列 (格式: {date_format}): {cols}")
                break

        print(f"   📊 昨日收盘价列: {yesterday_close_cols}")
        print(f"   📊 今日收盘价列: {today_close_cols}")
        print(f"   📊 今日开盘价列: {today_open_cols}")

        # 如果仍然找不到列，尝试更宽泛的匹配
        if not yesterday_close_cols:
            print("   🔍 尝试更宽泛的昨日价格列匹配...")
            # 尝试找任何包含"收盘价"的列
            all_close_cols = [col for col in df.columns if '收盘价' in col]
            print(f"   📋 所有收盘价列: {all_close_cols}")

            # 尝试找涨停价
            all_limit_cols = [col for col in df.columns if '涨停价' in col]
            print(f"   📋 所有涨停价列: {all_limit_cols}")

            if all_close_cols:
                # 如果有多个收盘价列，选择第一个
                yesterday_close_col = all_close_cols[0]
                print(f"   🔄 使用第一个收盘价列作为昨日价格: {yesterday_close_col}")
            elif all_limit_cols:
                yesterday_close_col = all_limit_cols[0]
                print(f"   🔄 使用第一个涨停价列作为昨日价格: {yesterday_close_col}")
            else:
                print("   ❌ 完全找不到价格相关列")
                return 0.0, 0.0, pd.DataFrame()
        else:
            yesterday_close_col = yesterday_close_cols[0]

        if not today_close_cols:
            print("   🔍 尝试更宽泛的今日收盘价列匹配...")
            all_close_cols = [col for col in df.columns if '收盘价' in col]
            if len(all_close_cols) >= 2:
                today_close_col = all_close_cols[1]  # 选择第二个收盘价列
                print(f"   🔄 使用第二个收盘价列作为今日收盘价: {today_close_col}")
            elif len(all_close_cols) == 1:
                today_close_col = all_close_cols[0]  # 如果只有一个，就用这个
                print(f"   🔄 使用唯一的收盘价列作为今日收盘价: {today_close_col}")
            else:
                print("   ❌ 找不到今日收盘价列")
                return 0.0, 0.0, pd.DataFrame()
        else:
            today_close_col = today_close_cols[0]

        if not today_open_cols:
            print("   🔍 尝试更宽泛的今日开盘价列匹配...")
            all_open_cols = [col for col in df.columns if '开盘价' in col]
            print(f"   📋 所有开盘价列: {all_open_cols}")
            if all_open_cols:
                today_open_col = all_open_cols[0]
                print(f"   🔄 使用第一个开盘价列作为今日开盘价: {today_open_col}")
            else:
                print("   ❌ 找不到今日开盘价列")
                return 0.0, 0.0, pd.DataFrame()
        else:
            today_open_col = today_open_cols[0]



        # 数据清洗 - 更安全的数值转换
        print(f"   🔧 开始数据清洗...")

        # 创建副本避免修改原数据
        df_clean = df.copy()

        # 安全的数值转换
        df_clean[yesterday_close_col] = pd.to_numeric(df_clean[yesterday_close_col], errors='coerce')
        df_clean[today_close_col] = pd.to_numeric(df_clean[today_close_col], errors='coerce')
        df_clean[today_open_col] = pd.to_numeric(df_clean[today_open_col], errors='coerce')

        # 显示转换统计
        yesterday_valid = df_clean[yesterday_close_col].notna().sum()
        today_close_valid = df_clean[today_close_col].notna().sum()
        today_open_valid = df_clean[today_open_col].notna().sum()

        print(f"   📊 数值转换结果:")
        print(f"      昨日价格: {yesterday_valid}/{len(df)} 有效")
        print(f"      今日收盘: {today_close_valid}/{len(df)} 有效")
        print(f"      今日开盘: {today_open_valid}/{len(df)} 有效")

        # 过滤有效数据
        valid_df = df_clean.dropna(subset=[yesterday_close_col, today_close_col, today_open_col])
        valid_df = valid_df[(valid_df[yesterday_close_col] > 0) &
                           (valid_df[today_close_col] > 0) &
                           (valid_df[today_open_col] > 0)]

        if valid_df.empty:
            print("❌ 没有有效的价格数据")
            return 0.0, 0.0

        # 计算昨日涨停收益：(今日收盘价 - 昨日收盘价) / 昨日收盘价
        yesterday_return = ((valid_df[today_close_col] - valid_df[yesterday_close_col]) /
                           valid_df[yesterday_close_col] * 100)

        # 计算今日接板收益：(今日收盘价 - 今日开盘价) / 昨日收盘价
        today_board_return = ((valid_df[today_close_col] - valid_df[today_open_col]) /
                             valid_df[yesterday_close_col] * 100)

        # 计算平均收益
        avg_yesterday_return = yesterday_return.mean()
        avg_today_board_return = today_board_return.mean()

        print(f"   📈 昨日涨停收益: {avg_yesterday_return:.2f}% (基于{len(valid_df)}只股票)")
        print(f"   📈 今日接板收益: {avg_today_board_return:.2f}% (基于{len(valid_df)}只股票)")

        # 返回收益数据和完整的DataFrame（用于晋级率计算）
        return round(avg_yesterday_return, 2), round(avg_today_board_return, 2), df

    except Exception as e:
        print(f"❌ 查询昨日涨停收益失败: {str(e)}")
        return 0.0, 0.0, pd.DataFrame()

def get_yesterday_limit_up_performance(selected_date, previous_date):
    """计算昨日涨停股票的收益指标（兼容性函数）"""
    yesterday_return, today_board_return, _ = get_yesterday_limit_up_performance_with_days(selected_date, previous_date)
    return yesterday_return, today_board_return

def calculate_market_stats(selected_date, selected_df, previous_date, poban_df, selected_limit_down_df, yesterday_return=0.0, today_board_return=0.0):
    """计算市场统计数据"""
    date_str = selected_date.strftime("%Y%m%d")
    stats = []

    # 查询市场概览数据
    try:
        market_overview = query_market_overview(selected_date)
    except Exception as e:
        print(f"⚠️ 市场概览查询失败，使用空数据: {str(e)}")
        market_overview = {}

    # 基础数据统计
    total_limit_up = safe_len(selected_df)
    total_limit_down = safe_len(selected_limit_down_df)
    total_poban = safe_len(poban_df)

    # 计算最高板
    max_board = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            max_board = pd.to_numeric(selected_df[days_col], errors='coerce').max()
            max_board = 0 if pd.isna(max_board) else int(max_board)

    # 计算破板率（破板家数/（涨停家数+破板家数））
    total_attempt = total_limit_up + total_poban  # 总尝试涨停家数
    poban_rate = round(total_poban / total_attempt * 100, 2) if total_attempt > 0 else 0

    # 使用传入的昨日涨停收益指标（已在前面计算）

    stats.extend([
        {'指标': '成交额(亿元)', '数值': round(market_overview.get('成交额', 0) / 100000000, 2)},
        {'指标': '上涨家数', '数值': market_overview.get('上涨家数', 0)},
        {'指标': '下跌家数', '数值': market_overview.get('下跌家数', 0)},
        {'指标': '涨停家数', '数值': total_limit_up},
        {'指标': '跌停家数', '数值': total_limit_down},
        {'指标': '最高板数', '数值': max_board},
        {'指标': '破板家数', '数值': total_poban},
        {'指标': '破板率', '数值': f"{poban_rate}%"},
        {'指标': '昨日涨停收益', '数值': f"{yesterday_return}%"},
        {'指标': '今日接板收益', '数值': f"{today_board_return}%"}
    ])

    return pd.DataFrame(stats)

def process_days_column(df, days_col):
    """处理连续涨停天数列"""
    df_copy = df.copy()
    if days_col in df_copy.columns:
        df_copy[days_col] = pd.to_numeric(df_copy[days_col], errors='coerce').fillna(1)
    else:
        df_copy[days_col] = 1
    return df_copy

def calculate_promotion_rates_detailed(current_df, previous_df, current_date, previous_date):
    """计算详细的连板晋级率"""
    if current_df is None or current_df.empty or previous_df is None or previous_df.empty:
        return pd.DataFrame()

    current_days_col = f'连续涨停天数[{current_date.strftime("%Y%m%d")}]'
    previous_days_col = f'连续涨停天数[{previous_date.strftime("%Y%m%d")}]'

    stats = []

    # 处理数据
    current_df_copy = process_days_column(current_df, current_days_col)
    previous_df_copy = process_days_column(previous_df, previous_days_col)

    # 统计各板块数量
    for i in range(1, 7):
        count = len(current_df_copy[current_df_copy[current_days_col] == i])
        stats.append({'指标': f'{i}板数量', '数值': count})

    # 保留6板以上数量统计
    count_6plus = len(current_df_copy[current_df_copy[current_days_col] >= 7])
    stats.append({'指标': '6板以上数量', '数值': count_6plus})



    # 计算晋级率
    total_prev = len(previous_df_copy)
    total_curr_promoted = len(current_df_copy[current_df_copy[current_days_col] > 1])
    total_rate = round(total_curr_promoted / total_prev * 100, 2) if total_prev > 0 else 0
    stats.append({'指标': '总晋级率', '数值': f"{total_rate}%"})

    for i in range(1, 7):
        prev_count = len(previous_df_copy[previous_df_copy[previous_days_col] == i])
        curr_count = len(current_df_copy[current_df_copy[current_days_col] == i + 1])
        rate = round(curr_count / prev_count * 100, 2) if prev_count > 0 else 0
        stats.append({'指标': f'{i}进{i+1}晋级率', '数值': f"{rate}%"})



    return pd.DataFrame(stats)

def analyze_single_date(query_date, export_excel=False, show_results=True, use_delay=False, excel_path=None, sheet_name='total'):
    """
    分析单个日期的市场数据

    Args:
        query_date: 查询日期
        export_excel (bool): 是否导出Excel
        show_results (bool): 是否显示结果
        use_delay (bool): 是否使用延时保护
        excel_path (str): Excel文件路径，如果为None则使用默认路径
        sheet_name (str): 工作表名称，默认为'total'
    """
    try:
        # 如果启用频率限制，等待必要的时间
        if use_delay:
            rate_limiter.wait_if_needed(show_results)
        # 处理日期格式
        if isinstance(query_date, str):
            if len(query_date) == 8:  # YYYYMMDD
                selected_date = datetime.strptime(query_date, '%Y%m%d').date()
            else:  # YYYY-MM-DD
                selected_date = datetime.strptime(query_date, '%Y-%m-%d').date()
        else:
            selected_date = query_date

        if show_results:
            print(f"🔍 分析日期: {selected_date.strftime('%Y-%m-%d')}")

        # 获取交易日历
        trade_dates_df = get_trade_dates()
        if trade_dates_df.empty:
            error_msg = "无法获取交易日数据"
            if show_results:
                print(f"❌ {error_msg}")
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'success': False,
                'error': error_msg
            }

        # 找到前一交易日
        trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
        previous_dates = [d for d in trading_dates if d < selected_date]
        if not previous_dates:
            error_msg = "没有找到前一交易日数据"
            if show_results:
                print(f"❌ {error_msg}")
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'success': False,
                'error': error_msg
            }

        previous_date = max(previous_dates)
        if show_results:
            print(f"📅 对比前一交易日: {previous_date.strftime('%Y-%m-%d')}")

        # 获取数据
        if show_results:
            print("📊 获取市场数据...")

        # 获取涨停数据
        try:
            selected_df = get_market_data(selected_date, 'limit_up')
        except Exception as e:
            print(f"⚠️ 获取涨停数据失败: {str(e)}")
            selected_df = None

        # 获取昨日涨停收益数据（同时包含连续涨停天数，替代前一日涨停查询）
        try:
            print(f"🔍 获取昨日涨停收益和连板数据...")
            yesterday_return, today_board_return, previous_df = get_yesterday_limit_up_performance_with_days(selected_date, previous_date)
        except Exception as e:
            print(f"⚠️ 获取昨日涨停收益数据失败: {str(e)}")
            yesterday_return, today_board_return, previous_df = 0.0, 0.0, None

        # 获取破板数据
        try:
            poban_df = get_market_data(selected_date, 'poban')
        except Exception as e:
            print(f"⚠️ 获取破板数据失败: {str(e)}")
            poban_df = None

        # 获取跌停数据
        try:
            selected_limit_down_df = get_market_data(selected_date, 'limit_down')
        except Exception as e:
            print(f"⚠️ 获取跌停数据失败: {str(e)}")
            selected_limit_down_df = None

        # 计算统计数据
        if show_results:
            print("📈 计算统计数据...")

        # 计算市场统计数据
        try:
            market_stats = calculate_market_stats(
                selected_date, selected_df, previous_date, poban_df, selected_limit_down_df,
                yesterday_return, today_board_return
            )
        except Exception as e:
            print(f"⚠️ 计算市场统计失败: {str(e)}")
            market_stats = pd.DataFrame()

        # 计算晋级率数据
        try:
            promotion_rates = calculate_promotion_rates_detailed(
                selected_df, previous_df, selected_date, previous_date
            )
        except Exception as e:
            print(f"⚠️ 计算晋级率失败: {str(e)}")
            promotion_rates = pd.DataFrame()

        # 计算概念统计
        try:
            concept_counts = get_concept_counts(selected_df, selected_date, min_count=2)
        except Exception as e:
            print(f"⚠️ 计算概念统计失败: {str(e)}")
            concept_counts = pd.DataFrame()

        # 查询行业资金流向
        try:
            inflow_data, outflow_data = query_industry_capital_flow(selected_date)
        except Exception as e:
            print(f"⚠️ 行业资金流向查询失败，使用空数据: {str(e)}")
            inflow_data, outflow_data = [], []

        # 查询概念指数涨跌幅
        try:
            concept_rise_data, concept_fall_data = query_concept_index_performance(selected_date)
        except Exception as e:
            print(f"⚠️ 概念指数查询失败，使用空数据: {str(e)}")
            concept_rise_data, concept_fall_data = [], []

        # 显示结果
        if show_results:
            print("\n📋 关键指标:")
            for _, row in market_stats.iterrows():
                print(f"   {row['指标']}: {row['数值']}")

            if not promotion_rates.empty:
                print("\n📊 连板晋级率分析:")
                for _, row in promotion_rates.iterrows():
                    print(f"   {row['指标']}: {row['数值']}")

            if not concept_counts.empty:
                print("\n🔥 热门概念 (出现≥2次):")
                for _, row in concept_counts.iterrows():
                    print(f"   {row['数值']}")

            # 显示行业资金流向
            if inflow_data or outflow_data:
                print("\n💰 行业资金流向:")
                if inflow_data:
                    print("   资金流入前3:")
                    for i, (name, amount) in enumerate(inflow_data, 1):
                        print(f"     {i}. {name}: {amount/100000000:.0f}亿元")
                if outflow_data:
                    print("   资金流出前3:")
                    for i, (name, amount) in enumerate(outflow_data, 1):
                        print(f"     {i}. {name}: {amount/100000000:.0f}亿元")

            # 显示概念指数涨跌幅
            if concept_rise_data or concept_fall_data:
                print("\n📈 概念指数表现:")
                if concept_rise_data:
                    print("   涨幅前10:")
                    for i, (name, change) in enumerate(concept_rise_data, 1):
                        print(f"     {i}. {name}: +{change:.1f}%")
                if concept_fall_data:
                    print("   跌幅前10:")
                    for i, (name, change) in enumerate(concept_fall_data, 1):
                        print(f"     {i}. {name}: {change:.1f}%")

        # 导出Excel
        if export_excel:
            # 确定输出文件路径
            if excel_path is None:
                output_dir = create_output_directory()
                filename = f"market_analysis_{selected_date.strftime('%Y%m%d')}.xlsx"
                output_file = os.path.join(output_dir, filename)
            else:
                output_file = excel_path

            try:
                # 检查文件是否已存在，如果存在则读取现有数据
                existing_data = {}
                other_sheets = {}
                if os.path.exists(output_file):
                    try:
                        excel_file = pd.ExcelFile(output_file)
                        if sheet_name in excel_file.sheet_names:
                            existing_df = pd.read_excel(output_file, sheet_name=sheet_name, index_col=0)
                            if not existing_df.empty:
                                for col in existing_df.columns:
                                    existing_data[col] = existing_df[col].to_dict()

                        # 保存其他sheet的数据
                        for sheet in excel_file.sheet_names:
                            if sheet != sheet_name:
                                other_sheets[sheet] = pd.read_excel(output_file, sheet_name=sheet)
                        excel_file.close()
                    except Exception as e:
                        if show_results:
                            print(f"⚠️ 读取现有文件时出错: {str(e)}")

                # 使用openpyxl直接操作，避免覆盖其他sheet
                import openpyxl
                from openpyxl.utils.dataframe import dataframe_to_rows

                # 创建市场统计汇总表
                ordered_summary_df = create_ordered_summary(
                    market_stats, promotion_rates, concept_counts,
                    inflow_data, outflow_data, concept_rise_data, concept_fall_data
                )

                if not ordered_summary_df.empty:
                    # 获取指标和数值
                    indicators = ordered_summary_df['指标'].tolist()
                    values = ordered_summary_df['数值'].tolist()

                    # 当前日期列名
                    current_date_col = selected_date.strftime('%Y-%m-%d')

                    # 合并现有数据和新数据
                    all_data = existing_data.copy()
                    all_data[current_date_col] = dict(zip(indicators, values))

                    # 创建完整的DataFrame
                    if all_data:
                        # 获取所有指标（保持顺序）
                        all_indicators = indicators  # 使用当前的指标顺序

                        # 获取所有日期列并排序（最新日期在前）
                        date_columns = sorted(all_data.keys(), reverse=True)

                        # 创建DataFrame
                        final_df = pd.DataFrame(index=all_indicators)
                        for date_col in date_columns:
                            final_df[date_col] = [all_data[date_col].get(indicator, '') for indicator in all_indicators]

                        # 重置索引，将指标作为第一列
                        final_df.reset_index(inplace=True)
                        final_df.rename(columns={'index': '指标'}, inplace=True)

                        # 在第一行插入日期行
                        date_row = ['日期'] + date_columns

                        # 创建包含日期行的完整DataFrame
                        complete_data = [date_row] + final_df.values.tolist()
                        complete_df = pd.DataFrame(complete_data)

                        # 如果文件存在，加载现有工作簿；否则创建新工作簿
                        if os.path.exists(output_file):
                            workbook = openpyxl.load_workbook(output_file)
                        else:
                            workbook = openpyxl.Workbook()
                            # 删除默认的Sheet
                            if 'Sheet' in workbook.sheetnames:
                                workbook.remove(workbook['Sheet'])

                        # 如果目标sheet已存在，删除它
                        if sheet_name in workbook.sheetnames:
                            workbook.remove(workbook[sheet_name])

                        # 创建新的sheet
                        worksheet = workbook.create_sheet(title=sheet_name)

                        # 写入数据
                        for r_idx, row in enumerate(dataframe_to_rows(complete_df, index=False, header=False), 1):
                            for c_idx, value in enumerate(row, 1):
                                worksheet.cell(row=r_idx, column=c_idx, value=value)

                        # 应用格式化
                        apply_excel_formatting_vertical(worksheet, complete_df)

                        # 保存工作簿
                        workbook.save(output_file)
                        workbook.close()

                if show_results:
                    print(f"✅ 数据已导出到: {output_file}")
                    print(f"📋 工作表: {sheet_name}")

            except Exception as e:
                if show_results:
                    print(f"❌ 导出Excel失败: {str(e)}")

        return {
            'date': selected_date.strftime('%Y-%m-%d'),
            'market_stats': market_stats,
            'promotion_rates': promotion_rates,
            'concept_counts': concept_counts,
            'industry_flow': {'inflow': inflow_data, 'outflow': outflow_data},
            'concept_performance': {'rise': concept_rise_data, 'fall': concept_fall_data},
            'raw_data': {
                'limit_up': selected_df,
                'limit_down': selected_limit_down_df,
                'poban': poban_df,
                'previous_limit_up': previous_df
            },
            'success': True
        }

    except Exception as e:
        error_msg = f"分析失败: {str(e)}"
        if show_results:
            print(f"❌ {error_msg}")

        return {
            'date': query_date,
            'success': False,
            'error': error_msg
        }





def analyze_date_range(start_date, end_date, export_excel=True, show_results=True, use_rate_limit=False, max_requests_per_minute=1, excel_path=None, sheet_name='total'):
    """
    分析日期范围内的市场数据，每个日期对应一列

    Args:
        start_date: 开始日期
        end_date: 结束日期
        export_excel (bool): 是否导出Excel
        show_results (bool): 是否显示结果
        use_rate_limit (bool): 是否使用频率限制
        max_requests_per_minute (int): 每分钟最多请求次数，默认1次
    """
    try:
        # 配置频率限制管理器
        if use_rate_limit:
            rate_limiter.enabled = True
            rate_limiter.set_rate_limit(max_requests_per_minute)
            rate_limiter.reset()
            if show_results:
                print(f"⏰ 频率限制已启用: 每分钟最多获取 {max_requests_per_minute} 个交易日数据")
        else:
            rate_limiter.enabled = False

        if show_results:
            print(f"🚀 开始分析日期范围: {start_date} 到 {end_date}")

        # 获取交易日
        trading_dates = get_trading_dates_in_range(start_date, end_date)
        if not trading_dates:
            if show_results:
                print("❌ 指定日期范围内没有交易日")
            return {'success': False, 'error': '指定日期范围内没有交易日'}

        if show_results:
            print(f"📅 将分析 {len(trading_dates)} 个交易日")

        # 收集所有交易日的数据
        all_data = {}
        success_count = 0

        for trade_date in trading_dates:
            date_str = trade_date.strftime('%Y-%m-%d')
            if show_results:
                print(f"📊 分析 {date_str}...")

            # 分析单日数据
            result = analyze_single_date(trade_date, export_excel=False, show_results=False, use_delay=use_rate_limit)

            if result['success']:
                # 使用有序汇总函数合并数据
                ordered_stats_df = create_ordered_summary(
                    result['market_stats'],
                    result['promotion_rates'],
                    result['concept_counts'],
                    result['industry_flow']['inflow'],
                    result['industry_flow']['outflow'],
                    result['concept_performance']['rise'],
                    result['concept_performance']['fall']
                )

                combined_stats = ordered_stats_df.to_dict('records') if not ordered_stats_df.empty else []
                all_data[date_str] = combined_stats
                success_count += 1

                if show_results:
                    print(f"✅ {date_str}: 数据收集成功")
            else:
                if show_results:
                    print(f"❌ {date_str}: 数据收集失败")

        if not all_data:
            if show_results:
                print("❌ 没有成功收集到任何数据")
            return {'success': False, 'error': '没有成功收集到任何数据'}

        # 创建列格式对比表格（指标为行，日期为列）
        # 首先收集所有可能的指标
        all_indicators = set()
        for date_data in all_data.values():
            for item in date_data:
                all_indicators.add(item['指标'])

        # 按照定义的顺序排序指标
        all_indicators = sort_indicators_by_order(list(all_indicators))

        # 创建列格式DataFrame：指标作为行，日期作为列
        # 按日期排序，最新日期在前
        sorted_dates = sorted(all_data.keys(), reverse=True)  # 最新日期在前

        # 创建DataFrame字典
        comparison_data = {'指标': all_indicators}

        for date_str in sorted_dates:
            date_data = all_data[date_str]
            # 创建指标到数值的映射
            indicator_map = {item['指标']: item['数值'] for item in date_data}

            # 为每个指标填充数值
            comparison_data[date_str] = [indicator_map.get(indicator, '') for indicator in all_indicators]

        comparison_df = pd.DataFrame(comparison_data)

        # 显示结果
        if show_results:
            print(f"\n📊 日期范围分析完成!")
            print(f"✅ 成功分析: {success_count}/{len(trading_dates)} 个交易日")
            print(f"📏 共收集 {len(all_indicators)} 个指标")

        # 导出Excel
        if export_excel:
            # 确定输出文件路径
            if excel_path is None:
                output_dir = create_output_directory()
                start_str = start_date if isinstance(start_date, str) else start_date.strftime('%Y%m%d')
                end_str = end_date if isinstance(end_date, str) else end_date.strftime('%Y%m%d')
                filename = f"market_range_analysis_{start_str}_to_{end_str}.xlsx"
                output_file = os.path.join(output_dir, filename)
            else:
                output_file = excel_path

            try:
                # 使用openpyxl直接操作，避免覆盖其他sheet
                import openpyxl
                from openpyxl.utils.dataframe import dataframe_to_rows

                # 在第一行插入日期行
                date_row = ['日期'] + sorted_dates

                # 创建包含日期行的完整DataFrame
                complete_data = [date_row] + comparison_df.values.tolist()
                complete_df = pd.DataFrame(complete_data)

                # 如果文件存在，加载现有工作簿；否则创建新工作簿
                if os.path.exists(output_file):
                    workbook = openpyxl.load_workbook(output_file)
                else:
                    workbook = openpyxl.Workbook()
                    # 删除默认的Sheet
                    if 'Sheet' in workbook.sheetnames:
                        workbook.remove(workbook['Sheet'])

                # 如果目标sheet已存在，删除它
                if sheet_name in workbook.sheetnames:
                    workbook.remove(workbook[sheet_name])

                # 创建新的sheet
                worksheet = workbook.create_sheet(title=sheet_name)

                # 写入数据
                for r_idx, row in enumerate(dataframe_to_rows(complete_df, index=False, header=False), 1):
                    for c_idx, value in enumerate(row, 1):
                        worksheet.cell(row=r_idx, column=c_idx, value=value)

                # 应用格式化
                apply_excel_formatting_vertical(worksheet, complete_df)

                # 保存工作簿
                workbook.save(output_file)
                workbook.close()

                if show_results:
                    print(f"✅ 数据已导出到: {output_file}")
                    print(f"📋 工作表: {sheet_name}")

                return {
                    'success': True,
                    'comparison_data': comparison_df,
                    'detailed_data': all_data,
                    'trading_dates': trading_dates,
                    'output_file': output_file
                }

            except Exception as e:
                if show_results:
                    print(f"❌ 导出Excel失败: {str(e)}")
                return {
                    'success': True,
                    'comparison_data': comparison_df,
                    'detailed_data': all_data,
                    'trading_dates': trading_dates,
                    'output_file': None,
                    'export_error': str(e)
                }

        return {
            'success': True,
            'comparison_data': comparison_df,
            'detailed_data': all_data,
            'trading_dates': trading_dates,
            'output_file': None
        }

    except Exception as e:
        error_msg = f"日期范围分析失败: {str(e)}"
        if show_results:
            print(f"❌ {error_msg}")
        return {'success': False, 'error': error_msg}

def supplement_excel_data(excel_file_path, sheet_name=None, end_date=None, show_results=True, use_rate_limit=False, max_requests_per_minute=1):
    """
    补充Excel数据功能

    Args:
        excel_file_path: Excel文件路径
        sheet_name: 要操作的sheet名称，如果为None则会提示用户选择
        end_date: 结束日期
        show_results (bool): 是否显示结果
        use_rate_limit (bool): 是否使用频率限制
        max_requests_per_minute (int): 每分钟最多请求次数
    """
    try:
        # 配置频率限制管理器
        if use_rate_limit:
            rate_limiter.enabled = True
            rate_limiter.set_rate_limit(max_requests_per_minute)
            rate_limiter.reset()
            if show_results:
                print(f"⏰ 频率限制已启用: 每分钟最多获取 {max_requests_per_minute} 个交易日数据")
        else:
            rate_limiter.enabled = False

        if show_results:
            print(f"🔄 开始补充Excel数据: {excel_file_path}")

        # 如果没有指定sheet，让用户选择
        if sheet_name is None:
            sheet_names, error = get_excel_sheets(excel_file_path)
            if error:
                if show_results:
                    print(f"❌ {error}")
                return {'success': False, 'error': error}

            if show_results:
                print(f"\n📋 Excel文件包含以下sheet:")
                for i, name in enumerate(sheet_names, 1):
                    print(f"   {i}. {name}")

                while True:
                    try:
                        choice = input(f"\n请选择要操作的sheet (1-{len(sheet_names)}): ").strip()
                        choice_idx = int(choice) - 1
                        if 0 <= choice_idx < len(sheet_names):
                            sheet_name = sheet_names[choice_idx]
                            print(f"✅ 已选择sheet: {sheet_name}")
                            break
                        else:
                            print(f"❌ 请输入1到{len(sheet_names)}之间的数字")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                    except KeyboardInterrupt:
                        print("\n⚠️ 操作被用户取消")
                        return {'success': False, 'error': '操作被用户取消'}

        # 读取现有Excel数据
        existing_df, latest_date, error = read_existing_excel_data(excel_file_path, sheet_name)

        if error:
            if show_results:
                print(f"❌ {error}")
            return {'success': False, 'error': error}

        if show_results:
            print(f"📅 Excel中最新日期: {latest_date.strftime('%Y-%m-%d')}")

        # 获取缺失的交易日
        missing_dates = get_missing_trading_dates(latest_date, end_date)

        if not missing_dates:
            if show_results:
                print("✅ 没有缺失的交易日，数据已是最新")
            return {'success': True, 'message': '没有缺失的交易日', 'added_dates': []}

        if show_results:
            print(f"📊 发现 {len(missing_dates)} 个缺失的交易日:")
            for missing_date in missing_dates:
                print(f"   - {missing_date.strftime('%Y-%m-%d')}")

        # 分析缺失日期的数据
        new_data = {}
        success_count = 0

        for missing_date in missing_dates:
            date_str = missing_date.strftime('%Y-%m-%d')
            if show_results:
                print(f"📈 分析 {date_str}...")

            # 分析单日数据
            result = analyze_single_date(missing_date, export_excel=False, show_results=False, use_delay=use_rate_limit)

            if result['success']:
                # 使用有序汇总函数合并数据
                ordered_stats_df = create_ordered_summary(
                    result['market_stats'],
                    result['promotion_rates'],
                    result['concept_counts'],
                    result['industry_flow']['inflow'],
                    result['industry_flow']['outflow'],
                    result['concept_performance']['rise'],
                    result['concept_performance']['fall']
                )

                if not ordered_stats_df.empty:
                    # 转换为列格式的数据
                    indicators = ordered_stats_df['指标'].tolist()
                    values = ordered_stats_df['数值'].tolist()

                    # 创建新列数据
                    new_data[date_str] = {indicator: value for indicator, value in zip(indicators, values)}
                    success_count += 1

                    if show_results:
                        print(f"✅ {date_str}: 数据收集成功")
                else:
                    if show_results:
                        print(f"❌ {date_str}: 数据为空")
            else:
                if show_results:
                    print(f"❌ {date_str}: 数据收集失败")

        if not new_data:
            if show_results:
                print("❌ 没有成功收集到任何新数据")
            return {'success': False, 'error': '没有成功收集到任何新数据'}

        # 将新数据添加到现有DataFrame（列格式）
        for date_str, indicator_values in new_data.items():
            # 为新日期添加列
            new_column_data = []
            for _, row in existing_df.iterrows():
                indicator = row['指标']
                new_column_data.append(indicator_values.get(indicator, ''))
            existing_df[date_str] = new_column_data

        # 重新排序列，最新日期在前（除了指标列）
        date_columns = [col for col in existing_df.columns if col != '指标']
        sorted_date_columns = sorted(date_columns, reverse=True)
        existing_df = existing_df[['指标'] + sorted_date_columns]

        # 在第一行插入日期行
        date_row = ['日期'] + sorted_date_columns

        # 创建包含日期行的完整DataFrame
        complete_data = [date_row] + existing_df.values.tolist()
        final_df = pd.DataFrame(complete_data)

        # 更新Excel文件
        try:
            # 读取原文件的所有sheet，保持其他sheet不变
            original_sheets = {}
            try:
                excel_file = pd.ExcelFile(excel_file_path)
                for sheet in excel_file.sheet_names:
                    if sheet != sheet_name:  # 保留其他sheet
                        original_sheets[sheet] = pd.read_excel(excel_file_path, sheet_name=sheet)
                excel_file.close()
            except Exception as e:
                if show_results:
                    print(f"⚠️ 读取其他sheet时出错: {str(e)}")

            # 使用openpyxl直接操作，避免覆盖其他sheet
            import openpyxl
            from openpyxl.utils.dataframe import dataframe_to_rows

            # 加载现有工作簿
            workbook = openpyxl.load_workbook(excel_file_path)

            # 如果目标sheet已存在，删除它
            if sheet_name in workbook.sheetnames:
                workbook.remove(workbook[sheet_name])

            # 创建新的sheet
            worksheet = workbook.create_sheet(title=sheet_name)

            # 写入数据
            for r_idx, row in enumerate(dataframe_to_rows(final_df, index=False, header=False), 1):
                for c_idx, value in enumerate(row, 1):
                    worksheet.cell(row=r_idx, column=c_idx, value=value)

            # 应用格式化
            apply_excel_formatting_vertical(worksheet, final_df)

            # 保存工作簿
            workbook.save(excel_file_path)
            workbook.close()

            if show_results:
                print(f"\n✅ Excel文件更新完成!")
                print(f"📁 文件路径: {excel_file_path}")
                print(f"📊 成功添加: {success_count}/{len(missing_dates)} 个交易日的数据")
                print(f"📅 新增日期: {', '.join(new_data.keys())}")

            return {
                'success': True,
                'excel_file': excel_file_path,
                'added_dates': list(new_data.keys()),
                'success_count': success_count,
                'total_missing': len(missing_dates)
            }

        except Exception as e:
            if show_results:
                print(f"❌ 更新Excel文件失败: {str(e)}")
            return {'success': False, 'error': f'更新Excel文件失败: {str(e)}'}

    except Exception as e:
        error_msg = f"补充数据失败: {str(e)}"
        if show_results:
            print(f"❌ {error_msg}")
        return {'success': False, 'error': error_msg}

def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        query_date = sys.argv[1]

        # 完整分析模式
        export_excel = len(sys.argv) > 2 and sys.argv[2].lower() in ['true', '1', 'yes']
        result = analyze_single_date(query_date, export_excel=export_excel, show_results=True)

        if not handle_result(result, f"分析完成: {result['date']}",
                           f"分析失败: {result['date']}"):
            sys.exit(1)
    else:
        # 交互模式
        print("🚀 涨停市场分析工具")
        print("=" * 50)
        print("1. 单日分析")
        print("2. 日期范围分析 (支持频率限制)")
        print("3. 补充Excel数据 (支持频率限制)")
        print("4. 查看频率限制功能说明")

        choice = input("请选择分析模式 (1/2/3/4): ").strip()

        if choice == '2':
            # 日期范围分析
            print("\n📅 日期范围分析模式")
            start_date = input("请输入开始日期 (YYYY-MM-DD 或 YYYYMMDD): ").strip()
            end_date = input("请输入结束日期 (YYYY-MM-DD 或 YYYYMMDD): ").strip()

            if not start_date or not end_date:
                print("❌ 请输入有效的开始和结束日期")
                return

            # 询问是否启用频率限制
            use_rate_limit_input = input("是否启用频率限制？(y/n，默认n): ").strip().lower()
            use_rate_limit = use_rate_limit_input in ['y', 'yes', '1', 'true']

            max_requests_per_minute = 1  # 默认频率
            if use_rate_limit:
                rate_input = input("请输入每分钟最多获取交易日数量 (1-5，默认1): ").strip()
                if rate_input.isdigit() and 1 <= int(rate_input) <= 5:
                    max_requests_per_minute = int(rate_input)
                print(f"✅ 频率限制已启用: 每分钟最多获取 {max_requests_per_minute} 个交易日数据")

            # 获取输出文件路径和工作表名称
            excel_path = get_excel_output_path()
            sheet_name = get_sheet_name_choice()

            result = analyze_date_range(start_date, end_date, export_excel=True, show_results=True,
                                      use_rate_limit=use_rate_limit, max_requests_per_minute=max_requests_per_minute,
                                      excel_path=excel_path, sheet_name=sheet_name)
            if result['success']:
                print(f"\n🎉 日期范围分析完成!")
                if result.get('output_file'):
                    print(f"📁 Excel文件: {result['output_file']}")
            else:
                print(f"\n❌ 日期范围分析失败: {result.get('error', '未知错误')}")
        elif choice == '3':
            # 补充Excel数据
            print("\n🔄 补充Excel数据模式")

            # 获取输出文件路径和工作表名称
            excel_file = get_excel_output_path()
            sheet_name = get_sheet_name_choice()

            # 询问是否指定结束日期
            end_date_input = input("请输入结束日期 (YYYY-MM-DD 或 YYYYMMDD，直接回车使用今天): ").strip()
            end_date = end_date_input if end_date_input else None

            # 询问是否启用频率限制
            use_rate_limit_input = input("是否启用频率限制？(y/n，默认n): ").strip().lower()
            use_rate_limit = use_rate_limit_input in ['y', 'yes', '1', 'true']

            max_requests_per_minute = 1  # 默认频率
            if use_rate_limit:
                rate_input = input("请输入每分钟最多获取交易日数量 (1-5，默认1): ").strip()
                if rate_input.isdigit() and 1 <= int(rate_input) <= 5:
                    max_requests_per_minute = int(rate_input)
                print(f"✅ 频率限制已启用: 每分钟最多获取 {max_requests_per_minute} 个交易日数据")

            result = supplement_excel_data(excel_file, sheet_name=sheet_name, end_date=end_date, show_results=True,
                                         use_rate_limit=use_rate_limit, max_requests_per_minute=max_requests_per_minute)
            if result['success']:
                print(f"\n🎉 Excel数据补充完成!")
                if result.get('added_dates'):
                    print(f"📅 新增日期: {', '.join(result['added_dates'])}")
                    print(f"📊 成功添加: {result['success_count']}/{result['total_missing']} 个交易日")
                else:
                    print(f"📋 {result.get('message', '数据已是最新')}")

            else:
                print(f"\n❌ Excel数据补充失败: {result.get('error', '未知错误')}")
        elif choice == '4':
            # 显示频率限制功能说明
            show_rate_limit_info()
            input("\n按回车键返回主菜单...")
            main()  # 重新显示菜单
        else:
            # 单日分析
            print("\n📊 单日分析模式")
            date_input = input("请输入日期 (YYYY-MM-DD 或 YYYYMMDD): ").strip()

            if not date_input:
                print("❌ 请输入有效日期")
                return

            # 询问是否导出Excel
            export_choice = input("是否导出Excel文件？(y/n，默认y): ").strip().lower()
            export_excel = export_choice not in ['n', 'no', '0', 'false']  # 默认为True

            excel_path = None
            sheet_name = 'total'

            if export_excel:
                # 获取输出文件路径和工作表名称
                excel_path = get_excel_output_path()
                sheet_name = get_sheet_name_choice()

            # 完整分析
            result = analyze_single_date(date_input, export_excel=export_excel, show_results=True,
                                       excel_path=excel_path, sheet_name=sheet_name)
            handle_result(result, "分析完成", "分析失败")

if __name__ == "__main__":
    main()