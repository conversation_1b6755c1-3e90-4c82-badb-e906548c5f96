aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
akshare==1.16.74
altair==5.5.0
APScheduler==3.11.0
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
beautifulsoup4 @ file:///home/<USER>/feedstock_root/build_artifacts/beautifulsoup4_1738740337718/work
blinker==1.9.0
Brot<PERSON> @ file:///D:/bld/brotli-split_1725267609074/work
cachetools==6.1.0
certifi @ file:///C:/b/abs_67mh6i36ci/croot/certifi_1752653721535/work/certifi
cffi @ file:///D:/bld/cffi_1725560792189/work
charset-normalizer @ file:///home/<USER>/feedstock_root/build_artifacts/charset-normalizer_1735929714516/work
click==8.1.8
colorama @ file:///home/<USER>/feedstock_root/build_artifacts/colorama_1733218098505/work
comm==0.2.2
contourpy @ file:///C:/b/abs_b0lpi9pj9c/croot/contourpy_1738161183484/work
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
DateTime==5.5
debugpy==1.8.14
decorator==5.2.1
et_xmlfile==2.0.0
exceptiongroup==1.3.0
executing==2.2.0
fake-useragent==1.5.1
fonttools @ file:///C:/b/abs_4crkswws2h/croot/fonttools_1737040078745/work
frozenlist==1.5.0
gitdb==4.0.12
GitPython==3.1.44
h2 @ file:///home/<USER>/feedstock_root/build_artifacts/h2_1738578511449/work
hpack @ file:///home/<USER>/feedstock_root/build_artifacts/hpack_1737618293087/work
html5lib @ file:///home/<USER>/feedstock_root/build_artifacts/html5lib_1734075161666/work
hyperframe @ file:///home/<USER>/feedstock_root/build_artifacts/hyperframe_1737618333194/work
idna @ file:///home/<USER>/feedstock_root/build_artifacts/idna_1733211830134/work
importlib_metadata==8.7.0
importlib_resources @ file:///C:/b/abs_e9f8_0_2dq/croot/importlib_resources-suite_1720641112288/work
ipykernel==6.29.5
ipython==8.18.1
jedi==0.19.2
Jinja2==3.1.6
jsonpath==0.82.2
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.6.3
jupyter_core==5.8.1
kiwisolver @ file:///C:/b/abs_88mdhvtahm/croot/kiwisolver_1672387921783/work
lxml==5.3.2
MarkupSafe==3.0.2
matplotlib==3.9.2
matplotlib-inline==0.1.7
mini-racer==0.12.4
multidict==6.3.2
narwhals==1.46.0
nest-asyncio==1.6.0
numpy @ file:///D:/bld/numpy_1732314314278/work/dist/numpy-2.0.2-cp39-cp39-win_amd64.whl#sha256=8824ea87142626db7bbf79bdbcbd5b3a2fd77d7570bd62b4f705100e42b651e1
openpyxl==3.1.5
packaging @ file:///C:/b/abs_3by6s2fa66/croot/packaging_1734472138782/work
pandas @ file:///D:/bld/pandas_1736810660203/work
parso==0.8.4
pillow @ file:///D:/bld/pillow_1746646223340/work
platformdirs==4.3.8
plotly==6.2.0
ply==3.11
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==6.31.1
psutil==7.0.0
pure_eval==0.2.3
py-mini-racer==0.6.0
pyarrow==20.0.0
pycparser @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_pycparser_1733195786/work
pydash==7.0.7
pydeck==0.9.1
PyExecJS==1.5.1
Pygments==2.19.2
pyparsing @ file:///C:/b/abs_40z8gyj9wi/croot/pyparsing_1731445739241/work
PyQt5==5.15.10
PyQt5_sip @ file:///C:/b/abs_d1i1jr9fgt/croot/pyqt-split_1736540551157/work/pyqt_sip
PySocks @ file:///D:/bld/pysocks_1733217287171/work
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1733215673016/work
pytz @ file:///home/<USER>/feedstock_root/build_artifacts/pytz_1706886791323/work
pywencai==0.13.1
pywin32==310
pyzmq==27.0.0
referencing==0.36.2
requests @ file:///home/<USER>/feedstock_root/build_artifacts/requests_1733217035951/work
rpds-py==0.26.0
sip @ file:///C:/b/abs_b6twfwtueg/croot/sip_1736544693649/work
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1733380938961/work
smmap==5.0.2
soupsieve @ file:///home/<USER>/feedstock_root/build_artifacts/soupsieve_1693929250441/work
stack-data==0.6.3
streamlit==1.46.1
tabulate==0.9.0
tenacity==9.1.2
toml==0.10.2
tomli @ file:///C:/Windows/TEMP/abs_ac109f85-a7b3-4b4d-bcfd-52622eceddf0hy332ojo/croots/recipe/tomli_1657175513137/work
tornado @ file:///C:/b/abs_7dzpc171lf/croot/tornado_1748956950306/work
tqdm @ file:///home/<USER>/feedstock_root/build_artifacts/tqdm_1735661334605/work
traitlets==5.14.3
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_typing_extensions_1743820059/work
tzdata @ file:///home/<USER>/feedstock_root/build_artifacts/python-tzdata_1742745135198/work
tzlocal==5.3.1
unicodedata2 @ file:///C:/b/abs_dfnftvxi4k/croot/unicodedata2_1736543771112/work
urllib3 @ file:///home/<USER>/feedstock_root/build_artifacts/urllib3_1734859416348/work
watchdog==6.0.0
wcwidth==0.2.13
webencodings @ file:///home/<USER>/feedstock_root/build_artifacts/webencodings_1733236011802/work
win_inet_pton @ file:///D:/bld/win_inet_pton_1733130564612/work
xlrd==2.0.1
yarl==1.19.0
zipp @ file:///C:/b/abs_2fcpcg89my/croot/zipp_1732630746332/work
zope.interface==7.2
zstandard==0.23.0
