#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的涨停输出脚本
"""

import sys
import os
from datetime import datetime, date, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from zhangtingshuchu import (
    get_trade_dates, 
    get_market_data, 
    calculate_market_stats,
    get_yesterday_limit_up_performance,
    export_market_analysis
)

def test_basic_functions():
    """测试基础功能"""
    print("=" * 50)
    print("测试基础功能")
    print("=" * 50)
    
    # 测试获取交易日历
    print("\n1. 测试获取交易日历...")
    trade_dates_df = get_trade_dates()
    if not trade_dates_df.empty:
        latest_date = trade_dates_df['trade_date'].dt.date.max()
        print(f"✅ 成功获取交易日历，最新交易日: {latest_date}")
        return latest_date
    else:
        print("❌ 获取交易日历失败")
        return None

def test_data_retrieval(test_date):
    """测试数据获取功能"""
    print("\n2. 测试数据获取...")
    
    # 测试获取涨停数据
    limit_up_df = get_market_data(test_date, 'limit_up')
    if limit_up_df is not None:
        print(f"✅ 成功获取涨停数据: {len(limit_up_df)}条")
    else:
        print("❌ 获取涨停数据失败")
    
    # 测试获取跌停数据
    limit_down_df = get_market_data(test_date, 'limit_down')
    if limit_down_df is not None:
        print(f"✅ 成功获取跌停数据: {len(limit_down_df)}条")
    else:
        print("❌ 获取跌停数据失败")
    
    return limit_up_df, limit_down_df

def test_calculation_functions(test_date, limit_up_df):
    """测试计算功能"""
    print("\n3. 测试计算功能...")
    
    if limit_up_df is None:
        print("❌ 无涨停数据，跳过计算测试")
        return
    
    # 获取前一交易日数据用于测试
    trade_dates_df = get_trade_dates()
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < test_date]
    
    if previous_dates:
        previous_date = max(previous_dates)
        previous_df = get_market_data(previous_date, 'limit_up')
        
        # 测试昨日涨停今日表现计算
        up_count, total_count, up_rate = get_yesterday_limit_up_performance(test_date, previous_df)
        print(f"✅ 昨日涨停今日表现: {up_count}/{total_count}, 上涨率: {up_rate}%")
        
        # 测试市场统计计算
        poban_df = get_market_data(test_date, 'poban')
        limit_down_df = get_market_data(test_date, 'limit_down')
        
        market_stats = calculate_market_stats(
            test_date, limit_up_df, previous_df, poban_df, limit_down_df
        )
        
        if not market_stats.empty:
            print("✅ 市场统计计算成功:")
            for _, row in market_stats.head(5).iterrows():
                print(f"   {row['指标']}: {row['数值']}")
        else:
            print("❌ 市场统计计算失败")
    else:
        print("❌ 无前一交易日数据，跳过计算测试")

def test_export_function(test_date):
    """测试导出功能"""
    print("\n4. 测试导出功能...")
    
    output_file = f"test_market_analysis_{test_date.strftime('%Y%m%d')}.xlsx"
    
    try:
        export_market_analysis(test_date, output_file)
        
        # 检查文件是否生成
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ 导出成功，文件大小: {file_size} bytes")
            
            # 清理测试文件
            try:
                os.remove(output_file)
                print("✅ 测试文件已清理")
            except:
                print("⚠️ 测试文件清理失败，请手动删除")
        else:
            print("❌ 导出文件未生成")
            
    except Exception as e:
        print(f"❌ 导出测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 开始测试优化后的涨停分析脚本")
    print("=" * 60)
    
    # 测试基础功能
    latest_date = test_basic_functions()
    if latest_date is None:
        print("❌ 基础功能测试失败，退出测试")
        return
    
    # 使用最新交易日进行测试
    test_date = latest_date
    print(f"\n📅 使用测试日期: {test_date}")
    
    # 测试数据获取
    limit_up_df, limit_down_df = test_data_retrieval(test_date)
    
    # 测试计算功能
    test_calculation_functions(test_date, limit_up_df)
    
    # 测试导出功能
    test_export_function(test_date)
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
