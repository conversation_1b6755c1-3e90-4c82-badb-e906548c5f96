import akshare as ak
import pandas as pd
import pywencai
from datetime import datetime, date, timedelta
import numpy as np

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)

def get_trade_dates():
    """获取交易日历数据"""
    try:
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def get_market_data(date, query_type):
    """获取市场数据
    query_type: 'limit_up'(涨停), 'limit_down'(跌停), 'poban'(破板), 'yesterday_limit_up'(昨日涨停)
    """
    query_map = {
        'limit_up': f"非ST,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,{date.strftime('%Y%m%d')}曾涨停",
        'yesterday_limit_up': f"非ST,{(date - timedelta(days=1)).strftime('%Y%m%d')}涨停"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        return df if df is not None and not df.empty else None
    except Exception as e:
        print(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_concept_counts(df, date, min_count=2):
    """统计涨停概念（出现次数大于等于min_count的）"""
    if df is None or df.empty:
        return pd.DataFrame()

    reason_col = f'涨停原因类别[{date.strftime("%Y%m%d")}]'
    if reason_col not in df.columns:
        return pd.DataFrame()

    try:
        concepts = df[reason_col].astype(str).str.split('+').explode().reset_index(drop=True)
        concept_counts = concepts.value_counts()
        # 只保留出现次数大于等于min_count的概念
        concept_counts = concept_counts[concept_counts >= min_count]
        # 转换为DataFrame格式
        concept_stats = [{'指标': '概念及出现次数', '数值': f"{concept}({count})"} 
                        for concept, count in concept_counts.items()]
        return pd.DataFrame(concept_stats)
    except Exception as e:
        print(f"统计概念时出错: {str(e)}")
        return pd.DataFrame()

def analyze_continuous_limit_up(df, date):
    """分析连续涨停数据"""
    if df is None or df.empty:
        return pd.DataFrame()

    date_str = date.strftime("%Y%m%d")
    required_columns = [
        f'连续涨停天数[{date_str}]', '股票代码', '股票简称', '最新价',
        f'涨停原因类别[{date_str}]', f'首次涨停时间[{date_str}]',
        f'最终涨停时间[{date_str}]', f'涨停封单量[{date_str}]',
        f'涨停封单额[{date_str}]', f'涨停类型[{date_str}]'
    ]

    available_columns = [col for col in required_columns if col in df.columns]
    if not available_columns:
        return pd.DataFrame()

    df_result = df[available_columns].copy()
    
    # 转换数值列和重命名列
    days_col = f'连续涨停天数[{date_str}]'
    if days_col in df_result.columns:
        # 转换为数值，将无效值设为1
        df_result[days_col] = pd.to_numeric(df_result[days_col], errors='coerce')
        df_result.loc[df_result[days_col].isna(), days_col] = 1
        df_result.rename(columns={days_col: '连续涨停天数'}, inplace=True)

    # 重命名其他列
    rename_dict = {
        f'涨停原因类别[{date_str}]': '涨停原因类别',
        f'首次涨停时间[{date_str}]': '首次涨停时间',
        f'最终涨停时间[{date_str}]': '最终涨停时间',
        f'涨停封单量[{date_str}]': '涨停封单量',
        f'涨停封单额[{date_str}]': '涨停封单额',
        f'涨停类型[{date_str}]': '涨停类型'
    }
    df_result.rename(columns=rename_dict, inplace=True)

    # 单位转换
    if '涨停封单量' in df_result.columns:
        df_result['涨停封单量(万)'] = round(df_result['涨停封单量'] / 10000, 2)
        df_result.drop('涨停封单量', axis=1, inplace=True)
    if '涨停封单额' in df_result.columns:
        df_result['涨停封单额(亿元)'] = round(df_result['涨停封单额'] / 100000000, 2)
        df_result.drop('涨停封单额', axis=1, inplace=True)

    return df_result.sort_values('连续涨停天数', ascending=False).reset_index(drop=True)

def calculate_market_stats(selected_date, selected_df, previous_df, poban_df, selected_limit_down_df):
    """计算市场统计数据"""
    date_str = selected_date.strftime("%Y%m%d")
    stats = []
    
    # 基础数据统计
    total_limit_up = len(selected_df) if selected_df is not None else 0
    total_limit_down = len(selected_limit_down_df) if selected_limit_down_df is not None else 0
    total_poban = len(poban_df) if poban_df is not None else 0
    
    # 计算首板数
    first_board_count = 0
    if selected_df is not None:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            first_board_count = len(selected_df[selected_df[days_col] == 1])
    
    # 计算最高板
    max_board = 0
    if selected_df is not None and f'连续涨停天数[{date_str}]' in selected_df.columns:
        max_board = selected_df[f'连续涨停天数[{date_str}]'].max()
    
    # 计算破板率（破板家数/（涨停家数+破板家数））
    total_attempt = total_limit_up + total_poban  # 总尝试涨停家数
    poban_rate = round(total_poban / total_attempt * 100, 2) if total_attempt > 0 else 0
    
    # 计算昨日涨停今日上涨率
    yesterday_up_rate = 0
    if previous_df is not None:
        yesterday_limit_up_stocks = set(previous_df['股票代码'].tolist())
        today_up_stocks = 0
        if selected_df is not None:
            # 检查必要的列是否存在
            if '最新涨跌幅' in selected_df.columns:
                for stock in yesterday_limit_up_stocks:
                    stock_data = selected_df[selected_df['股票代码'] == stock]
                    if not stock_data.empty:
                        try:
                            # 获取涨跌幅数据
                            change_rate = pd.to_numeric(stock_data['最新涨跌幅'].iloc[0], errors='coerce')
                            # 如果涨跌幅大于0，说明上涨
                            if pd.notna(change_rate) and change_rate > 0:
                                today_up_stocks += 1
                        except (ValueError, TypeError, IndexError):
                            continue

        yesterday_up_rate = round(today_up_stocks / len(yesterday_limit_up_stocks) * 100, 2) if yesterday_limit_up_stocks else 0
    
    stats.extend([
        {'指标': '日期', '数值': selected_date.strftime('%Y-%m-%d')},
        {'指标': '涨停家数', '数值': total_limit_up},
        {'指标': '跌停家数', '数值': total_limit_down},
        {'指标': '首板家数', '数值': first_board_count},
        {'指标': '最高板数', '数值': max_board},
        {'指标': '破板家数', '数值': total_poban},
        {'指标': '破板率', '数值': f"{poban_rate}%"},
        {'指标': '昨日涨停今日上涨率', '数值': f"{yesterday_up_rate}%"}
    ])
    
    return pd.DataFrame(stats)

def calculate_promotion_rates_detailed(current_df, previous_df, current_date, previous_date):
    """计算详细的连板晋级率"""
    if current_df is None or previous_df is None:
        return pd.DataFrame()

    current_days_col = f'连续涨停天数[{current_date.strftime("%Y%m%d")}]'
    previous_days_col = f'连续涨停天数[{previous_date.strftime("%Y%m%d")}]'
    
    stats = []

    # 计算各板块数量
    if current_days_col in current_df.columns:
        current_df[current_days_col] = pd.to_numeric(current_df[current_days_col], errors='coerce')
        # 统计1-7板的数量
        for i in range(1, 8):
            count = len(current_df[current_df[current_days_col] == i])
            stats.append({'指标': f'{i}板数量', '数值': count})
        
        # 统计8板及以上
        count_8plus = len(current_df[current_df[current_days_col] >= 8])
        if count_8plus > 0:
            stats.append({'指标': '8板及以上数量', '数值': count_8plus})

    # 计算晋级率
    if previous_days_col in previous_df.columns and current_days_col in current_df.columns:
        previous_df[previous_days_col] = pd.to_numeric(previous_df[previous_days_col], errors='coerce')
        current_df[current_days_col] = pd.to_numeric(current_df[current_days_col], errors='coerce')
        
        # 计算总晋级率
        total_prev = len(previous_df)
        total_curr = len(current_df[current_df[current_days_col] > 1])
        total_rate = round(total_curr / total_prev * 100, 2) if total_prev > 0 else 0
        stats.append({'指标': '总晋级率', '数值': f"{total_rate}%"})

        # 计算1-7板的晋级率
        for i in range(1, 8):
            prev_count = len(previous_df[previous_df[previous_days_col] == i])
            curr_count = len(current_df[current_df[current_days_col] == i + 1])
            rate = round(curr_count / prev_count * 100, 2) if prev_count > 0 else 0
            stats.append({'指标': f'{i}进{i+1}晋级率', '数值': f"{rate}%"})

        # 计算8板及以上晋级率
        prev_count_8plus = len(previous_df[previous_df[previous_days_col] >= 8])
        curr_count_9plus = len(current_df[current_df[current_days_col] >= 9])
        rate_8plus = round(curr_count_9plus / prev_count_8plus * 100, 2) if prev_count_8plus > 0 else 0
        if prev_count_8plus > 0:
            stats.append({'指标': '8板及以上晋级率', '数值': f"{rate_8plus}%"})

    return pd.DataFrame(stats)

def export_market_analysis(selected_date, output_file):
    """导出市场分析数据到Excel"""
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日数据")
        return

    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < selected_date]
    if not previous_dates:
        print("没有找到前一交易日数据")
        return
    
    previous_date = max(previous_dates)
    print(f"分析日期: {selected_date.strftime('%Y-%m-%d')} (对比前一交易日: {previous_date.strftime('%Y-%m-%d')})")

    # 获取数据
    selected_df = get_market_data(selected_date, 'limit_up')
    previous_df = get_market_data(previous_date, 'limit_up')
    poban_df = get_market_data(selected_date, 'poban')
    selected_limit_down_df = get_market_data(selected_date, 'limit_down')

    # 创建市场统计数据
    market_stats = calculate_market_stats(
        selected_date, selected_df, previous_df, poban_df, selected_limit_down_df
    )
    
    # 计算详细晋级率（不包含日期）
    promotion_rates = calculate_promotion_rates_detailed(selected_df, previous_df, selected_date, previous_date)
    
    # 获取涨停概念统计（出现次数≥2的）
    concept_counts = get_concept_counts(selected_df, selected_date, min_count=2)

    # 合并所有统计数据到一个sheet
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 合并所有统计数据（去除重复的日期和空行）
        all_stats = pd.concat([
            market_stats,
            promotion_rates.iloc[1:] if not promotion_rates.empty else pd.DataFrame(),  # 跳过日期行
            concept_counts
        ], ignore_index=True)
        
        # 写入合并后的统计数据
        all_stats.to_excel(writer, sheet_name='市场统计汇总', index=False)

    print(f"数据已成功导出到 {output_file}")

def main():
    print("正在获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    
    if not trade_dates_df.empty:
        latest_trade_date = trade_dates_df['trade_date'].dt.date.max()
        earliest_trade_date = trade_dates_df['trade_date'].dt.date.min()
        
        print(f"\n可选日期范围: {earliest_trade_date} 至 {latest_trade_date}")
        print("\n选择方式：")
        print("1. 输入具体日期（格式：YYYY-MM-DD）")
        print("2. 输入相对日期（如：-1 表示最新交易日的前一天，-2 表示前两天）")
        print("3. 直接回车使用最新交易日")
        
        while True:
            date_input = input("\n请输入日期或相对天数: ").strip()
            
            try:
                if not date_input:  # 直接回车
                    selected_date = latest_trade_date
                    break
                elif date_input.startswith('-'):  # 相对日期
                    days_back = int(date_input)
                    all_dates = sorted(trade_dates_df['trade_date'].dt.date.unique())
                    latest_idx = all_dates.index(latest_trade_date)
                    target_idx = latest_idx + days_back
                    if 0 <= target_idx < len(all_dates):
                        selected_date = all_dates[target_idx]
                        break
                    else:
                        print("超出可选范围，请重新输入")
                else:  # 具体日期
                    input_date = datetime.strptime(date_input, '%Y-%m-%d').date()
                    if earliest_trade_date <= input_date <= latest_trade_date:
                        all_dates = trade_dates_df['trade_date'].dt.date
                        if input_date in all_dates.values:
                            selected_date = input_date
                        else:
                            selected_date = all_dates[all_dates <= input_date].max()
                        break
                    else:
                        print("日期超出范围，请重新输入")
            except ValueError:
                print("输入格式错误，请重新输入")
        
        print(f"\n已选择日期: {selected_date}")
        output_file = f"market_analysis_{selected_date.strftime('%Y%m%d')}.xlsx"
        export_market_analysis(selected_date, output_file)
    else:
        print("获取交易日历数据失败")

if __name__ == "__main__":
    main() 