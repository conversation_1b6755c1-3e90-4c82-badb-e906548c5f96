import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
from pywencai import get_data


def get_recent_trading_days(n=10):
    """获取最近n个交易日（含今天）"""
    trade_date_df = ak.tool_trade_date_hist_sina()
    trade_date_df['trade_date'] = pd.to_datetime(trade_date_df['trade_date'])
    today = datetime.now().date()
    # 只保留今天及之前的交易日
    trade_date_df = trade_date_df[trade_date_df['trade_date'].dt.date <= today]
    # 取最近n个交易日，使用pandas标准排序方式
    trade_date_df = trade_date_df.sort_values('trade_date', ascending=False)
    recent_days = trade_date_df.head(n)['trade_date']
    return list(recent_days.dt.strftime('%Y-%m-%d'))[::-1]  # 升序返回，使用标准日期格式


def get_market_data():
    # 获取最近10个交易日
    trading_days = get_recent_trading_days(10)
    print(f"获取到的交易日列表: {trading_days}")
    
    result = []
    for day in trading_days:
        print(f"\n处理日期: {day}")
        try:
            # 获取大盘指数数据
            sh_df = ak.stock_zh_index_daily_em(symbol="sh000001")  # 上证指数
            sz_df = ak.stock_zh_index_daily_em(symbol="sz399006")  # 创业板指
            
            # 转换日期格式以匹配
            sh_df['date'] = pd.to_datetime(sh_df['date']).dt.strftime('%Y-%m-%d')
            sz_df['date'] = pd.to_datetime(sz_df['date']).dt.strftime('%Y-%m-%d')
            
            # 过滤到指定日期
            sh_row = sh_df[sh_df['date'] == day]
            sz_row = sz_df[sz_df['date'] == day]
            
            print(f"上证数据行数: {len(sh_row)}, 创业板数据行数: {len(sz_row)}")
            
            if sh_row.empty or sz_row.empty:
                print(f"日期 {day} 数据不完整，跳过")
                continue
                
            sh_close = sh_row.iloc[0]['close']
            sz_close = sz_row.iloc[0]['close']
            
            # 计算涨跌幅（与前一日对比）
            sh_idx = list(sh_df[sh_df['date'] == day].index)
            sz_idx = list(sz_df[sz_df['date'] == day].index)
            
            if len(sh_idx) == 0 or len(sz_idx) == 0:
                print(f"未找到日期 {day} 的指数数据")
                sh_change = None
                sz_change = None
            elif int(sh_idx[0]) == 0 or int(sz_idx[0]) == 0:
                print(f"日期 {day} 为第一条数据，无法计算涨跌幅")
                sh_change = None
                sz_change = None
            else:
                sh_last_close = sh_df.iloc[int(sh_idx[0]) - 1]['close']
                sz_last_close = sz_df.iloc[int(sz_idx[0]) - 1]['close']
                sh_change = (sh_close - sh_last_close) / sh_last_close * 100
                sz_change = (sz_close - sz_last_close) / sz_last_close * 100
            
            # 获取当日A股行情数据
            try:
                # 使用问财获取市场统计数据
                query = f"{day}涨停家数，跌停家数，上涨家数，下跌家数，成交额"
                market_data = get_data(query)
                
                if not market_data.empty:
                    # 提取数据
                    limit_up = int(market_data.loc[0, '涨停家数'])
                    limit_down = int(market_data.loc[0, '跌停家数'])
                    up_count = int(market_data.loc[0, '上涨家数'])
                    down_count = int(market_data.loc[0, '下跌家数'])
                    total_volume = float(market_data.loc[0, '成交额']) / 100  # 转换为亿
                    
                    print(f"获取到市场统计数据：成交量 {total_volume:.2f}亿")
                    print(f"上涨/下跌/涨停/跌停: {up_count}/{down_count}/{limit_up}/{limit_down}")
                else:
                    print(f"未获取到{day}的市场统计数据")
                    total_volume = up_count = down_count = limit_up = limit_down = 0
                
            except Exception as e:
                print(f"获取市场统计数据失败: {str(e)}")
                total_volume = up_count = down_count = limit_up = limit_down = 0
            
            # 修改格式化输出方式
            sh_change_str = f"{sh_change:.2f}" if sh_change is not None else "0.00"
            sz_change_str = f"{sz_change:.2f}" if sz_change is not None else "0.00"
            print(f"上证收盘: {sh_close:.2f}, 涨跌幅: {sh_change_str}%")
            print(f"创业板收盘: {sz_close:.2f}, 涨跌幅: {sz_change_str}%")
            
            result.append({
                '日期': day,
                '上证指数': sh_close,
                '上证涨跌幅%': sh_change if sh_change is not None else 0,
                '创业板指': sz_close,
                '创业板涨跌幅%': sz_change if sz_change is not None else 0,
                '沪深总成交(亿)': total_volume,
                '上涨家数': up_count,
                '下跌家数': down_count,
                '涨停数': limit_up,
                '跌停数': limit_down
            })
            
        except Exception as e:
            print(f"处理日期 {day} 时发生错误: {str(e)}")
            continue
    
    if not result:
        print("警告：未获取到任何数据！")
        return
        
    df = pd.DataFrame(result)
    print("\n最终数据预览:")
    print(df)
    
    # 保存到Excel
    df.to_excel('market_data_last10days.xlsx', index=False)
    print('\n数据已保存到 market_data_last10days.xlsx')

if __name__ == "__main__":
    get_market_data()