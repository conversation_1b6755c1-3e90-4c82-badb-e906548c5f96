#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境安装脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✓ {description}成功")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"✗ {description}失败")
            if result.stderr:
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ {description}异常: {str(e)}")
        return False
    
    return True

def install_environment():
    """安装环境"""
    print("="*60)
    print("开始安装Python环境依赖")
    print("="*60)
    
    # 1. 检查Python版本
    print(f"\n1. 检查Python环境:")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 2. 升级pip
    if not run_command("python -m pip install --upgrade pip", "升级pip"):
        print("pip升级失败，但继续安装...")
    
    # 3. 安装核心依赖（简化版本，避免conda特定的包）
    core_packages = [
        "pandas>=1.5.0",
        "numpy>=1.20.0", 
        "akshare>=1.16.0",
        "pywencai>=0.13.0",
        "openpyxl>=3.1.0",
        "xlsxwriter>=3.0.0",
        "requests>=2.28.0",
        "beautifulsoup4>=4.11.0",
        "lxml>=4.9.0",
        "matplotlib>=3.5.0",
        "plotly>=5.0.0",
        "streamlit>=1.40.0"
    ]
    
    print(f"\n3. 安装核心依赖包:")
    for package in core_packages:
        if not run_command(f"pip install {package}", f"安装 {package}"):
            print(f"警告: {package} 安装失败，但继续...")
    
    # 4. 尝试从requirements.txt安装（跳过conda特定包）
    print(f"\n4. 从requirements.txt安装（跳过conda包）:")
    
    requirements_path = "stock/trust/test/requirements.txt"
    if os.path.exists(requirements_path):
        # 读取requirements.txt并过滤掉conda特定的包
        with open(requirements_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 创建过滤后的requirements
        filtered_requirements = []
        skip_patterns = [
            "@ file://",  # conda特定路径
            "pywin32",    # Windows特定，可能需要特殊处理
            "PyQt5",      # 可能需要特殊安装
        ]
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 检查是否需要跳过
                should_skip = any(pattern in line for pattern in skip_patterns)
                if not should_skip:
                    # 提取包名和版本
                    if '@' in line:
                        package_name = line.split('@')[0].strip()
                        # 尝试获取版本号
                        if '==' in package_name:
                            filtered_requirements.append(package_name)
                    else:
                        filtered_requirements.append(line)
        
        # 写入临时requirements文件
        temp_req_path = "temp_requirements.txt"
        with open(temp_req_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(filtered_requirements))
        
        # 安装过滤后的requirements
        run_command(f"pip install -r {temp_req_path}", "从过滤后的requirements.txt安装")
        
        # 清理临时文件
        try:
            os.remove(temp_req_path)
        except:
            pass
    else:
        print(f"requirements.txt文件不存在: {requirements_path}")
    
    # 5. 验证关键包安装
    print(f"\n5. 验证关键包安装:")
    key_packages = ['pandas', 'numpy', 'akshare', 'pywencai', 'openpyxl', 'streamlit']
    
    for package in key_packages:
        try:
            __import__(package)
            print(f"✓ {package}: 已安装")
        except ImportError:
            print(f"✗ {package}: 未安装")
            # 尝试重新安装
            run_command(f"pip install {package}", f"重新安装 {package}")
    
    # 6. 特殊处理pywin32（Windows特定）
    if sys.platform.startswith('win'):
        print(f"\n6. 安装Windows特定包:")
        run_command("pip install pywin32", "安装pywin32")
    
    print(f"\n" + "="*60)
    print("环境安装完成")
    print("="*60)
    
    # 7. 最终验证
    print(f"\n最终验证:")
    try:
        import pywencai
        print("✓ pywencai导入成功")
        
        # 测试基础功能
        result = pywencai.get(query="A股")
        if result is not None:
            print("✓ pywencai基础功能正常")
        else:
            print("⚠ pywencai查询返回None，可能需要登录")
            
    except Exception as e:
        print(f"✗ pywencai验证失败: {str(e)}")
    
    print(f"\n建议:")
    print(f"1. 如果pywencai需要登录，请运行: python -c \"import pywencai; pywencai.login()\"")
    print(f"2. 如果仍有问题，请运行诊断脚本: python stock/trust/diagnose_environment.py")
    print(f"3. 重启Python环境后再次测试")

if __name__ == "__main__":
    install_environment()
