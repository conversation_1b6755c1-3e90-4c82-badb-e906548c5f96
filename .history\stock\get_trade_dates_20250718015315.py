import akshare as ak
import pandas as pd
from datetime import datetime, date

def get_trade_dates():
    """获取交易日历数据"""
    try:
        # 获取新浪交易日历数据
        trade_dates_df = ak.tool_trade_date_hist_sina()
        
        # 转换日期格式
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def filter_dates(df, year, month):
    """筛选指定年月的交易日期"""
    if df.empty:
        return pd.DataFrame()
    
    # 筛选指定年月的数据
    mask = (df['trade_date'].dt.year == year) & (df['trade_date'].dt.month == month)
    filtered_df = df[mask].copy()
    
    # 添加星期几列
    filtered_df['weekday'] = filtered_df['trade_date'].dt.day_name()
    
    # 重新排序列
    filtered_df = filtered_df[['trade_date', 'weekday']]
    
    # 重命名列
    filtered_df.columns = ['交易日期', '星期']
    
    return filtered_df

def export_to_excel(df, year, month):
    """导出数据到Excel"""
    if df.empty:
        print("没有数据可导出")
        return
    
    # 设置输出文件名
    filename = f"trade_dates_{year}_{month:02d}.xlsx"
    
    try:
        # 设置日期格式
        df['交易日期'] = df['交易日期'].dt.strftime('%Y-%m-%d')
        
        # 导出到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已成功导出到 {filename}")
    except Exception as e:
        print(f"导出数据时出错: {str(e)}")

def main():
    # 设置目标年月
    target_year = 2025
    target_month = 7
    
    # 获取交易日历数据
    print("正在获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    
    if not trade_dates_df.empty:
        # 筛选指定年月的数据
        print(f"正在筛选 {target_year}年{target_month}月 的数据...")
        filtered_df = filter_dates(trade_dates_df, target_year, target_month)
        
        if not filtered_df.empty:
            # 打印预览
            print("\n数据预览:")
            print(filtered_df)
            print(f"\n共有 {len(filtered_df)} 个交易日\n")
            
            # 导出到Excel
            export_to_excel(filtered_df, target_year, target_month)
        else:
            print(f"未找到 {target_year}年{target_month}月 的交易日期数据")
    else:
        print("获取交易日历数据失败")

if __name__ == "__main__":
    main() 