import os
from pptx import Presentation
from datetime import datetime
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn

def export_ppt_notes(ppt_path, output_dir=None):
    """
    导出PPT中所有幻灯片的备注到txt和docx文件
    
    Args:
        ppt_path (str): PPT文件的路径
        output_dir (str, optional): 输出目录，默认为PPT所在目录
    
    Returns:
        tuple: (txt文件路径, docx文件路径)
    """
    try:
        # 检查PPT文件是否存在
        if not os.path.exists(ppt_path):
            raise FileNotFoundError(f"找不到PPT文件: {ppt_path}")
            
        # 获取输出目录
        if output_dir is None:
            output_dir = os.path.dirname(ppt_path)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        ppt_name = os.path.splitext(os.path.basename(ppt_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_txt = os.path.join(output_dir, f"{ppt_name}_notes_{timestamp}.txt")
        output_docx = os.path.join(output_dir, f"{ppt_name}_notes_{timestamp}.docx")
        
        # 打开PPT文件
        prs = Presentation(ppt_path)
        
        # 创建Word文档
        doc = Document()
        # 设置默认字体为仿宋GB2312
        doc.styles['Normal'].font.name = '仿宋_GB2312'
        doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
        # 设置行间距为固定值29磅
        doc.styles['Normal'].paragraph_format.line_spacing = Pt(29)
        
        # 添加标题
        doc.add_heading(f"PPT文件备注导出 - {ppt_name}", 0)
        doc.add_paragraph(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        doc.add_paragraph("=" * 40)
        
        # 创建txt输出文件
        with open(output_txt, 'w', encoding='utf-8') as f:
            f.write(f"PPT文件名: {ppt_name}\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("-" * 50 + "\n\n")
            
            # 遍历所有幻灯片
            for slide_number, slide in enumerate(prs.slides, 1):
                # 获取备注
                notes = slide.notes_slide
                notes_text = notes.notes_text_frame.text.strip() if notes else ""
                
                # 写入txt文件
                f.write(f"第 {slide_number} 张幻灯片:\n")
                if notes_text:
                    f.write(f"备注内容:\n{notes_text}\n")
                else:
                    f.write("【无备注】\n")
                f.write("-" * 30 + "\n\n")
                
                # 写入Word文档
                doc.add_paragraph(f"第 {slide_number} 张幻灯片:")
                if notes_text:
                    doc.add_paragraph(f"备注内容:\n{notes_text}")
                else:
                    doc.add_paragraph("【无备注】")
                doc.add_paragraph("-" * 30)
        
        # 保存Word文档
        doc.save(output_docx)
        
        print(f"备注已导出到:\nTXT文件: {output_txt}\nWord文档: {output_docx}")
        return output_txt, output_docx
        
    except Exception as e:
        print(f"导出过程中出现错误: {str(e)}")
        return None, None

if __name__ == "__main__":
    # 使用示例
    ppt_path = input("请输入PPT文件的路径: ").strip('"')  # 去除可能的引号
    export_ppt_notes(ppt_path)
