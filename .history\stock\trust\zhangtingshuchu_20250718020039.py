import akshare as ak
import pandas as pd
import pywencai
from datetime import datetime, date, timedelta
import numpy as np

# 设置目标年月
target_year = 2025
target_months = [5, 6]  # 可以添加多个月份

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)

def get_trade_dates():
    """获取交易日历数据"""
    try:
        # 获取新浪交易日历数据
        trade_dates_df = ak.tool_trade_date_hist_sina()
        
        # 转换日期格式
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def get_market_data(date, query_type):
    """获取市场数据
    query_type: 'limit_up'(涨停), 'limit_down'(跌停), 'poban'(破板)
    """
    query_map = {
        'limit_up': f"非ST,{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,{date.strftime('%Y%m%d')}曾涨停"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        return df if not df.empty else None
    except Exception as e:
        print(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_concept_counts(df, date):
    """统计涨停概念"""
    if df is None or df.empty:
        return pd.DataFrame()

    reason_col = f'涨停原因类别[{date.strftime("%Y%m%d")}]'
    if reason_col not in df.columns:
        return pd.DataFrame()

    try:
        concepts = df[reason_col].astype(str).str.split('+').explode().reset_index(drop=True)
        concept_counts = concepts.value_counts().reset_index()
        concept_counts.columns = ['概念', '出现次数']
        return concept_counts
    except Exception as e:
        print(f"统计概念时出错: {str(e)}")
        return pd.DataFrame()

def analyze_continuous_limit_up(df, date):
    """分析连续涨停数据"""
    if df is None or df.empty:
        return pd.DataFrame()

    date_str = date.strftime("%Y%m%d")
    required_columns = [
        f'连续涨停天数[{date_str}]', '股票代码', '股票简称', '最新价',
        f'涨停原因类别[{date_str}]', f'首次涨停时间[{date_str}]',
        f'最终涨停时间[{date_str}]', f'涨停封单量[{date_str}]',
        f'涨停封单额[{date_str}]', f'涨停类型[{date_str}]'
    ]

    available_columns = [col for col in required_columns if col in df.columns]
    if not available_columns:
        return pd.DataFrame()

    df_result = df[available_columns].copy()
    
    # 转换数值列
    days_col = f'连续涨停天数[{date_str}]'
    if days_col in df_result.columns:
        df_result[days_col] = pd.to_numeric(df_result[days_col], errors='coerce').fillna(1)

    # 重命名列
    rename_dict = {
        days_col: '连续涨停天数',
        f'涨停原因类别[{date_str}]': '涨停原因类别',
        f'首次涨停时间[{date_str}]': '首次涨停时间',
        f'最终涨停时间[{date_str}]': '最终涨停时间',
        f'涨停封单量[{date_str}]': '涨停封单量',
        f'涨停封单额[{date_str}]': '涨停封单额',
        f'涨停类型[{date_str}]': '涨停类型'
    }
    df_result.rename(columns=rename_dict, inplace=True)

    # 单位转换
    if '涨停封单量' in df_result.columns:
        df_result['涨停封单量(万)'] = round(df_result['涨停封单量'] / 10000, 2)
        df_result.drop('涨停封单量', axis=1, inplace=True)
    if '涨停封单额' in df_result.columns:
        df_result['涨停封单额(亿元)'] = round(df_result['涨停封单额'] / 100000000, 2)
        df_result.drop('涨停封单额', axis=1, inplace=True)

    return df_result.sort_values('连续涨停天数', ascending=False).reset_index(drop=True)

def calculate_promotion_rates(current_df, previous_df, current_date, previous_date):
    """计算连板晋级率"""
    if current_df is None or previous_df is None:
        return pd.DataFrame()

    current_days_col = f'连续涨停天数[{current_date.strftime("%Y%m%d")}]'
    previous_days_col = f'连续涨停天数[{previous_date.strftime("%Y%m%d")}]'

    promotion_data = []
    max_days = 0

    if current_days_col in current_df.columns:
        current_df[current_days_col] = pd.to_numeric(current_df[current_days_col], errors='coerce')
        max_days = max(max_days, current_df[current_days_col].max() or 0)

    if previous_days_col in previous_df.columns:
        previous_df[previous_days_col] = pd.to_numeric(previous_df[previous_days_col], errors='coerce')
        max_days = max(max_days, previous_df[previous_days_col].max() or 0)

    for days in range(1, int(max_days) + 1):
        prev_count = 0
        if previous_days_col in previous_df.columns:
            prev_count = len(previous_df[previous_df[previous_days_col] == days])

        curr_count = 0
        if current_days_col in current_df.columns:
            curr_count = len(current_df[current_df[current_days_col] == days + 1])

        rate = round(curr_count / prev_count * 100) if prev_count > 0 else 0
        
        promotion_data.append({
            '连板数': f"{days} → {days + 1}板",
            '前一日数量': prev_count,
            '晋级数量': curr_count,
            '晋级率': f"{rate}%"
        })

    return pd.DataFrame(promotion_data)

def export_market_analysis(selected_date, output_file):
    """导出市场分析数据到Excel"""
    # 获取交易日历
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日数据")
        return

    # 获取前一交易日
    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < selected_date]
    if not previous_dates:
        print("没有找到前一交易日数据")
        return
    
    previous_date = max(previous_dates)
    print(f"分析日期: {selected_date.strftime('%Y-%m-%d')} (对比前一交易日: {previous_date.strftime('%Y-%m-%d')})")

    # 创建Excel写入器
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 获取并导出涨停数据
        selected_df = get_market_data(selected_date, 'limit_up')
        previous_df = get_market_data(previous_date, 'limit_up')
        poban_df = get_market_data(selected_date, 'poban')
        selected_limit_down_df = get_market_data(selected_date, 'limit_down')
        previous_limit_down_df = get_market_data(previous_date, 'limit_down')

        # 1. 市场统计指标
        stats_data = []
        if selected_df is not None and previous_df is not None:
            # 计算各项指标
            yesterday_zt_stocks = previous_df['股票代码'].tolist() if previous_df is not None else []
            today_zt_stocks = selected_df['股票代码'].tolist() if selected_df is not None else []
            poban_stocks = poban_df['股票代码'].tolist() if poban_df is not None else []

            stats_data.extend([
                {'指标': '涨停家数', '今日': len(today_zt_stocks), '昨日': len(yesterday_zt_stocks)},
                {'指标': '跌停家数', '今日': len(selected_limit_down_df) if selected_limit_down_df is not None else 0,
                 '昨日': len(previous_limit_down_df) if previous_limit_down_df is not None else 0},
                {'指标': '破板家数', '数量': len(poban_stocks)}
            ])
        
        pd.DataFrame(stats_data).to_excel(writer, sheet_name='市场统计', index=False)

        # 2. 涨停概念分析
        if selected_df is not None:
            concept_counts = get_concept_counts(selected_df, selected_date)
            if not concept_counts.empty:
                concept_counts.to_excel(writer, sheet_name='涨停概念统计', index=False)

        # 3. 连续涨停分析
        if selected_df is not None:
            continuous_data = analyze_continuous_limit_up(selected_df, selected_date)
            if not continuous_data.empty:
                continuous_data.to_excel(writer, sheet_name='连续涨停分析', index=False)

        # 4. 连板晋级率
        if selected_df is not None and previous_df is not None:
            promotion_rates = calculate_promotion_rates(selected_df, previous_df, selected_date, previous_date)
            if not promotion_rates.empty:
                promotion_rates.to_excel(writer, sheet_name='连板晋级率', index=False)

        # 5. 原始涨停数据
        if selected_df is not None:
            selected_df.to_excel(writer, sheet_name='涨停股票列表', index=False)

        print(f"数据已成功导出到 {output_file}")

def main():
    # 获取交易日历数据
    print("正在获取交易日历数据...")
    trade_dates_df = get_trade_dates()
    
    if not trade_dates_df.empty:
        # 获取最新交易日
        today = datetime.now().date()
        latest_trade_date = trade_dates_df['trade_date'].dt.date.max()
        earliest_trade_date = trade_dates_df['trade_date'].dt.date.min()
        
        # 显示可选日期范围
        print(f"\n可选日期范围: {earliest_trade_date} 至 {latest_trade_date}")
        print("\n选择方式：")
        print("1. 输入具体日期（格式：YYYY-MM-DD）")
        print("2. 输入相对日期（如：-1 表示最新交易日的前一天，-2 表示前两天）")
        print("3. 直接回车使用最新交易日")
        
        while True:
            date_input = input("\n请输入日期或相对天数: ").strip()
            
            try:
                if not date_input:  # 直接回车
                    selected_date = latest_trade_date
                    break
                elif date_input.startswith('-'):  # 相对日期
                    days_back = int(date_input)
                    # 获取所有交易日期并排序
                    all_dates = sorted(trade_dates_df['trade_date'].dt.date.unique())
                    # 找到最新交易日的索引
                    latest_idx = all_dates.index(latest_trade_date)
                    # 计算目标日期的索引
                    target_idx = latest_idx + days_back
                    if 0 <= target_idx < len(all_dates):
                        selected_date = all_dates[target_idx]
                        break
                    else:
                        print("超出可选范围，请重新输入")
                else:  # 具体日期
                    input_date = datetime.strptime(date_input, '%Y-%m-%d').date()
                    if earliest_trade_date <= input_date <= latest_trade_date:
                        # 找到最近的交易日
                        all_dates = trade_dates_df['trade_date'].dt.date
                        if input_date in all_dates.values:
                            selected_date = input_date
                        else:
                            # 找到小于等于输入日期的最近交易日
                            selected_date = all_dates[all_dates <= input_date].max()
                        break
                    else:
                        print("日期超出范围，请重新输入")
            except ValueError:
                print("输入格式错误，请重新输入")
        
        print(f"\n已选择日期: {selected_date}")
        
        # 导出数据
        output_file = f"market_analysis_{selected_date.strftime('%Y%m%d')}.xlsx"
        export_market_analysis(selected_date, output_file)
    else:
        print("获取交易日历数据失败")

if __name__ == "__main__":
    main() 