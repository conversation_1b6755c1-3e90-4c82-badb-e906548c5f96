#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于AI智能分类的涨停股票归类工具
使用DeepSeek或其他AI API进行概念分类
"""

import pywencai
import pandas as pd
from datetime import datetime, date
import warnings
import os
import json
import requests
warnings.filterwarnings('ignore')

def create_output_directory():
    """创建输出目录"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(script_dir, "ai_limit_up_output")
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    return output_dir

def get_limit_up_stocks(query_date):
    """获取涨停股票数据"""
    try:
        print(f"🔍 查询 {query_date.strftime('%Y%m%d')} 涨停股票数据...")
        
        query = f"非ST,沪深主板,{query_date.strftime('%Y%m%d')}涨停"
        print(f"🔍 执行查询: {query}")
        
        df = pywencai.get(
            query=query,
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        
        if df is None or df.empty:
            print("❌ 查询结果为空")
            return None
            
        print(f"✅ 获取到 {len(df)} 只涨停股票")
        return df
        
    except Exception as e:
        print(f"❌ 获取涨停股票数据失败: {str(e)}")
        return None

def call_deepseek_api(prompt, api_key=None):
    """
    调用DeepSeek API进行智能分类
    
    Args:
        prompt: 提示词
        api_key: API密钥
        
    Returns:
        str: AI返回的分类结果
    """
    try:
        # DeepSeek API配置
        url = "https://api.deepseek.com/v1/chat/completions"
        
        # 如果没有提供API密钥，使用环境变量或默认值
        if not api_key:
            api_key = os.getenv('DEEPSEEK_API_KEY', '***********************************  ')
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的股票概念分类专家，擅长根据股票名称和涨停原因进行概念归类。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 2000
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"❌ DeepSeek API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ AI API调用失败: {str(e)}")
        return None

def call_kimi_api(prompt, api_key=None):
    """
    调用Kimi API进行智能分类
    
    Args:
        prompt: 提示词
        api_key: API密钥
        
    Returns:
        str: AI返回的分类结果
    """
    try:
        # Kimi API配置
        url = "https://api.moonshot.cn/v1/chat/completions"
        
        if not api_key:
            api_key = os.getenv('KIMI_API_KEY', 'your-api-key-here')
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        data = {
            "model": "moonshot-v1-8k",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的股票概念分类专家，擅长根据股票名称和涨停原因进行概念归类。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 2000
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            print(f"❌ Kimi API调用失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Kimi API调用失败: {str(e)}")
        return None

def create_classification_prompt(stocks_data, user_keywords):
    """
    创建AI分类的提示词
    
    Args:
        stocks_data: 股票数据DataFrame
        user_keywords: 用户提供的关键词列表
        
    Returns:
        str: 构建的提示词
    """
    # 准备股票信息
    stock_info = []
    for _, row in stocks_data.iterrows():
        stock_name = row.get('股票简称', '')
        reason = row.get('涨停原因', '无')
        board_num = row.get('连板数', 1)
        
        stock_info.append(f"股票: {stock_name}, 连板数: {board_num}, 涨停原因: {reason}")
    
    stock_list = "\n".join(stock_info)
    keywords_str = "、".join(user_keywords)
    
    prompt = f"""
请根据以下用户提供的关键词对涨停股票进行智能分类：

用户关键词: {keywords_str}

涨停股票信息:
{stock_list}

请按照以下要求进行分类：
1. 第一行显示用户提供的关键词作为分类标题
2. 在每个关键词下面列出相关的股票名称
3. 如果某个股票不属于任何用户关键词，请归类到"其他"
4. 请考虑股票名称、涨停原因和行业特征进行智能匹配
5. 输出格式为：
   关键词1: 股票1、股票2、股票3
   关键词2: 股票4、股票5
   其他: 股票6、股票7

请直接输出分类结果，不要添加额外说明。
"""
    
    return prompt

def parse_ai_classification(ai_response):
    """
    解析AI返回的分类结果
    
    Args:
        ai_response: AI返回的文本
        
    Returns:
        dict: 分类结果字典
    """
    try:
        classification = {}
        
        if not ai_response:
            return classification
            
        lines = ai_response.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            if ':' in line or '：' in line:
                # 处理中英文冒号
                separator = ':' if ':' in line else '：'
                parts = line.split(separator, 1)
                
                if len(parts) == 2:
                    category = parts[0].strip()
                    stocks_str = parts[1].strip()
                    
                    # 解析股票名称（支持多种分隔符）
                    stocks = []
                    for sep in ['、', ',', '，', ' ']:
                        if sep in stocks_str:
                            stocks = [s.strip() for s in stocks_str.split(sep) if s.strip()]
                            break
                    
                    if not stocks and stocks_str:
                        stocks = [stocks_str]
                    
                    classification[category] = stocks
        
        return classification
        
    except Exception as e:
        print(f"❌ 解析AI分类结果失败: {str(e)}")
        return {}

def ai_classify_limit_up_stocks(query_date, user_keywords, ai_provider='deepseek', api_key=None, show_results=True):
    """
    使用AI进行涨停股票智能分类
    
    Args:
        query_date: 查询日期
        user_keywords: 用户提供的关键词列表
        ai_provider: AI提供商 ('deepseek' 或 'kimi')
        api_key: API密钥
        show_results: 是否显示结果
        
    Returns:
        dict: 分类结果
    """
    try:
        # 处理日期格式
        if isinstance(query_date, str):
            if len(query_date) == 8:
                selected_date = datetime.strptime(query_date, '%Y%m%d').date()
            else:
                selected_date = datetime.strptime(query_date, '%Y-%m-%d').date()
        else:
            selected_date = query_date
            
        if show_results:
            print(f"🤖 AI智能分类 {selected_date.strftime('%Y-%m-%d')} 涨停股票...")
            print(f"🏷️ 用户关键词: {', '.join(user_keywords)}")
            print(f"🔧 AI提供商: {ai_provider.upper()}")
            
        # 获取涨停股票数据
        limit_up_df = get_limit_up_stocks(selected_date)
        
        if limit_up_df is None:
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'keywords': user_keywords,
                'classification': {},
                'raw_data': pd.DataFrame(),
                'success': False,
                'error': '未获取到涨停股票数据'
            }
            
        # 处理股票数据
        date_str = selected_date.strftime('%Y%m%d')
        days_col = f'连续涨停天数[{date_str}]'
        reason_col = f'涨停原因类别[{date_str}]'
        
        # 查找相似列
        if days_col not in limit_up_df.columns:
            possible_days_cols = [col for col in limit_up_df.columns if '连续涨停' in col or '涨停天数' in col]
            days_col = possible_days_cols[0] if possible_days_cols else None
            
        if reason_col not in limit_up_df.columns:
            possible_reason_cols = [col for col in limit_up_df.columns if '涨停原因' in col or '概念' in col]
            reason_col = possible_reason_cols[0] if possible_reason_cols else None
            
        # 准备数据
        df_work = limit_up_df.copy()
        
        if days_col:
            df_work['连板数'] = pd.to_numeric(df_work[days_col], errors='coerce').fillna(1).astype(int)
        else:
            df_work['连板数'] = 1
            
        if reason_col:
            df_work['涨停原因'] = df_work[reason_col].fillna('无').astype(str)
        else:
            df_work['涨停原因'] = '无'
            
        # 创建AI分类提示词
        prompt = create_classification_prompt(df_work, user_keywords)
        
        if show_results:
            print("🤖 正在调用AI进行智能分类...")
            
        # 调用AI API
        if ai_provider.lower() == 'kimi':
            ai_response = call_kimi_api(prompt, api_key)
        else:
            ai_response = call_deepseek_api(prompt, api_key)
            
        if not ai_response:
            return {
                'date': selected_date.strftime('%Y-%m-%d'),
                'keywords': user_keywords,
                'classification': {},
                'raw_data': df_work,
                'success': False,
                'error': 'AI API调用失败'
            }
            
        # 解析AI分类结果
        classification = parse_ai_classification(ai_response)
        
        if show_results:
            print("\n🎯 AI智能分类结果:")
            print("=" * 60)
            
            for category, stocks in classification.items():
                print(f"\n📂 {category}:")
                if stocks:
                    for i, stock in enumerate(stocks, 1):
                        print(f"   {i}. {stock}")
                else:
                    print("   (无)")
                    
            print(f"\n📊 分类统计:")
            total_classified = sum(len(stocks) for stocks in classification.values())
            print(f"   总分类股票: {total_classified}只")
            print(f"   分类数量: {len(classification)}个")
            
        return {
            'date': selected_date.strftime('%Y-%m-%d'),
            'keywords': user_keywords,
            'classification': classification,
            'raw_data': df_work,
            'ai_response': ai_response,
            'success': True
        }
        
    except Exception as e:
        error_msg = f"AI智能分类失败: {str(e)}"
        if show_results:
            print(f"❌ {error_msg}")
            
        return {
            'date': str(query_date),
            'keywords': user_keywords,
            'classification': {},
            'raw_data': pd.DataFrame(),
            'success': False,
            'error': error_msg
        }

def export_ai_classification(result, output_dir=None):
    """导出AI分类结果到Excel"""
    try:
        if not result['success']:
            print("❌ 无法导出，AI分类失败")
            return None

        if not output_dir:
            output_dir = create_output_directory()

        date_str = result['date'].replace('-', '')
        filename = f"ai_classification_{date_str}.xlsx"
        excel_path = os.path.join(output_dir, filename)

        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # AI分类结果工作表
            classification_data = []
            max_stocks = max([len(stocks) for stocks in result['classification'].values()]) if result['classification'] else 0

            # 创建分类表格
            categories = list(result['classification'].keys())

            # 第一行：关键词标题
            header_row = categories
            classification_data.append(header_row)

            # 后续行：股票名称
            for i in range(max_stocks):
                row = []
                for category in categories:
                    stocks = result['classification'].get(category, [])
                    if i < len(stocks):
                        row.append(stocks[i])
                    else:
                        row.append('')
                classification_data.append(row)

            # 创建DataFrame并导出
            classification_df = pd.DataFrame(classification_data)
            classification_df.to_excel(writer, sheet_name='AI智能分类', index=False, header=False)

            # 统计信息工作表
            stats_data = []
            stats_data.append(['分类', '股票数量'])
            for category, stocks in result['classification'].items():
                stats_data.append([category, len(stocks)])

            stats_df = pd.DataFrame(stats_data[1:], columns=stats_data[0])
            stats_df.to_excel(writer, sheet_name='分类统计', index=False)

            # 原始数据工作表
            if not result['raw_data'].empty:
                key_columns = ['股票简称', '股票代码', '连板数', '涨停原因']
                for col in result['raw_data'].columns:
                    if any(keyword in col for keyword in ['收盘价', '涨跌幅', '成交额', '市值']):
                        if col not in key_columns:
                            key_columns.append(col)

                available_columns = [col for col in key_columns if col in result['raw_data'].columns]
                raw_df = result['raw_data'][available_columns]
                raw_df.to_excel(writer, sheet_name='原始数据', index=False)

        print(f"✅ AI分类结果已导出: {excel_path}")
        return excel_path

    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")
        return None

def get_recent_trading_dates(days=5):
    """获取最近几个交易日"""
    from datetime import timedelta

    dates = []
    current = date.today()

    while len(dates) < days:
        if current.weekday() < 5:  # 0-4 是周一到周五
            dates.append(current)
        current -= timedelta(days=1)

    return sorted(dates, reverse=True)

def main():
    """主函数"""
    import sys

    print("🤖 AI智能涨停分类工具")
    print("=" * 50)

    if len(sys.argv) > 1:
        # 命令行模式（简化版）
        query_date = sys.argv[1]
        keywords = ["AI", "新能源", "医药", "军工", "消费"]  # 默认关键词

        print(f"📅 查询日期: {query_date}")
        print(f"🏷️ 使用默认关键词: {', '.join(keywords)}")

        result = ai_classify_limit_up_stocks(query_date, keywords)

        if result['success']:
            export_path = export_ai_classification(result)
            print(f"\n✅ AI分类完成")
            if export_path:
                print(f"📄 结果文件: {export_path}")
        else:
            print(f"\n❌ AI分类失败: {result.get('error', '未知错误')}")
    else:
        # 交互模式
        print("请按照提示进行操作：")

        # 选择日期
        print("\n📅 选择查询日期:")
        print("1. 输入指定日期")
        print("2. 选择最近交易日")

        date_choice = input("请选择 (1/2): ").strip()

        if date_choice == '2':
            recent_dates = get_recent_trading_dates(5)
            print("\n📅 最近交易日:")
            for i, d in enumerate(recent_dates, 1):
                weekday = ['周一','周二','周三','周四','周五','周六','周日'][d.weekday()]
                print(f"   {i}. {d.strftime('%Y-%m-%d')} ({weekday})")

            date_idx = input("请选择日期 (1-5): ").strip()
            try:
                date_index = int(date_idx) - 1
                if 0 <= date_index < len(recent_dates):
                    query_date = recent_dates[date_index]
                else:
                    print("❌ 选择无效")
                    return
            except ValueError:
                print("❌ 请输入有效数字")
                return
        else:
            date_input = input("请输入日期 (YYYY-MM-DD 或 YYYYMMDD): ").strip()
            if not date_input:
                print("❌ 请输入有效日期")
                return
            query_date = date_input

        # 输入关键词
        print("\n🏷️ 输入分类关键词:")
        print("请输入您想要的分类关键词，用逗号分隔")
        print("例如: AI,新能源,医药,军工,消费")

        keywords_input = input("关键词: ").strip()
        if not keywords_input:
            keywords = ["AI", "新能源", "医药", "军工", "消费"]
            print(f"使用默认关键词: {', '.join(keywords)}")
        else:
            keywords = [k.strip() for k in keywords_input.split(',') if k.strip()]

        # 选择AI提供商
        print("\n🤖 选择AI提供商:")
        print("1. DeepSeek (推荐)")
        print("2. Kimi")

        ai_choice = input("请选择 (1/2，默认1): ").strip()
        ai_provider = 'kimi' if ai_choice == '2' else 'deepseek'

        # 执行AI分类
        result = ai_classify_limit_up_stocks(query_date, keywords, ai_provider=ai_provider)

        if result['success']:
            export_path = export_ai_classification(result)
            print(f"\n✅ AI智能分类完成")
            if export_path:
                print(f"📄 结果文件: {export_path}")

            # 显示使用提示
            print(f"\n💡 提示:")
            print(f"   1. 请确保已设置API密钥环境变量")
            print(f"   2. DeepSeek: DEEPSEEK_API_KEY")
            print(f"   3. Kimi: KIMI_API_KEY")
        else:
            print(f"\n❌ AI分类失败: {result.get('error', '未知错误')}")
            if 'API' in result.get('error', ''):
                print("💡 请检查API密钥设置和网络连接")

if __name__ == "__main__":
    main()
