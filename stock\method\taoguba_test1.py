from bs4 import BeautifulSoup
import requests
import os
import re

# Function to sanitize filename
def sanitize_filename(filename):
    return re.sub(r'[^\w\s-]', '', filename).strip().replace(' ', '_')

# Function to extract content and images as Markdown
def extract_content_to_markdown(soup):
    content_div = soup.find('div', class_='tzitem_text hideText')
    if not content_div:
        return "No content found"
    
    markdown_lines = []
    
    # Extract all elements in order
    for element in content_div.children:
        if element.name == 'b':
            # Bold text as Markdown
            text = element.get_text(strip=True)
            if text:
                markdown_lines.append(f"**{text}**")
        elif element.name == 'br':
            # Line breaks
            markdown_lines.append("")
        elif element.name == 'div' and element.find('img'):
            # Images as Markdown
            img = element.find('img')
            img_url = img.get('data-original') or img.get('src')
            if img_url and img_url != 'https://www.tgb.cn/placeHolder.png':
                markdown_lines.append(f"![Image]({img_url})")
        elif element.name == 'a':
            # Links as Markdown
            text = element.get_text(strip=True)
            href = element.get('href')
            if text and href:
                markdown_lines.append(f"[{text}]({href})")
            elif text:
                markdown_lines.append(text)
        else:
            # Plain text
            text = element.get_text(strip=True) if isinstance(element, BeautifulSoup) else str(element).strip()
            if text:
                markdown_lines.append(text)
    
    # Join lines, remove excessive newlines
    return '\n'.join(line for line in markdown_lines if line)

# Read URLs from the text file
with open('淘股吧.txt', 'r', encoding='utf-8') as file:
    urls = [line.strip() for line in file if line.strip()]

# Process each URL
for url in urls:
    try:
        # Fetch the webpage content
        response = requests.get(url)
        response.raise_for_status()
        
        # Parse HTML to extract the title
        soup = BeautifulSoup(response.text, 'html.parser')
        span = soup.find('span', id='ztgioMsg')
        if span and 'subject' in span.attrs:
            title = span['subject']
        else:
            title = 'default_title'  # Fallback title if not found
        
        # Sanitize the title for use as a filename
        sanitized_title = sanitize_filename(title)
        
        # Define output Markdown filename
        output_filename = f"{sanitized_title}.md"
        
        # Extract content as Markdown
        content = extract_content_to_markdown(soup)
        
        # Write to Markdown file
        with open(output_filename, 'w', encoding='utf-8') as md_file:
            md_file.write(f"# {title}\n\n")
            md_file.write(f"Source URL: {url}\n\n")
            md_file.write(content)
        
        print(f"成功创建 Markdown 文件: {output_filename}")
        
    except Exception as e:
        print(f"处理 URL {url} 时出错: {str(e)}")
