import os
import pandas as pd

# 定义主文件路径
main_file = r'F:\sata15-17512529587\dav\Documents\2022-水安全保障规划监督管理\3-规划实施管理台账\24-1114-年报（部门）+年度工作（部门）\台账\司局意见\test\黄委.xls'

# 定义文件夹路径
folder_path = r'F:\sata15-17512529587\dav\Documents\2022-水安全保障规划监督管理\3-规划实施管理台账\24-1114-年报（部门）+年度工作（部门）\台账\司局意见\test'

# 检查文件夹是否存在
if not os.path.exists(folder_path):
    print(f"错误：文件夹 '{folder_path}' 不存在！")
    # 如果需要，可以创建文件夹
    os.makedirs(folder_path, exist_ok=True)
    print(f"已创建文件夹 '{folder_path}'。")

# 检查主文件是否存在
if not os.path.exists(main_file):
    print(f"错误：文件 '{main_file}' 不存在！")
    exit()


# 遍历文件夹中的所有Excel文件
for file_name in os.listdir(folder_path):
    if file_name.endswith('.xls') or file_name.endswith('.xlsx'):
        file_path = os.path.join(folder_path, file_name)
        
        # 遍历当前文件的每个sheet
        for sheet_name, df in df_dict.items():
            # 如果当前sheet在主文件中存在
            if sheet_name in main_df_dict:
                # 获取主文件的对应sheet
                main_df = main_df_dict[sheet_name]
                
                # 遍历当前sheet的每一行
                for index, row in df.iterrows():
                    # 检查当前行的列数是否足够
                    if len(row) < 6:
                        print(f"警告：文件 '{file_name}' 的 sheet '{sheet_name}' 第 {index + 1} 行列数不足，跳过该行。")
                        continue
                    
                    # 获取A列（序号）和F列（工作进展）的值
                    serial_number = row.iloc[0]  # A列，索引为0
                    content = row.iloc[5]  # F列，索引为5
                    
                    # 检查 content 是否为 NaN 或其他空值
                    if pd.isna(content):
                        content = ""  # 将 NaN 转换为空字符串
                    else:
                        content = str(content)  # 确保 content 是字符串
                    
                    # 在主文件中找到对应的序号
                    mask = main_df.iloc[:, 0] == serial_number  # 主文件的A列
                    if mask.any():
                        # 获取主文件中对应序号的工作进展
                        main_content = main_df.loc[mask, main_df.columns[5]].values[0]  # 主文件的F列
                        
                        # 检查 main_content 是否为 NaN 或其他空值
                        if pd.isna(main_content):
                            main_content = ""  # 将 NaN 转换为空字符串
                        else:
                            main_content = str(main_content)  # 确保 main_content 是字符串
                        
                        # 检查内容是否已经存在
                        if content not in main_content:
                            # 在主文件的对应序号的工作进展列添加内容，并附上文件名
                            main_df.loc[mask, main_df.columns[5]] += f" ({file_name}) {content}"
        
# 保存修改后的主文件
with pd.ExcelWriter(main_file, engine='openpyxl') as writer:
    for sheet_name, df in main_df_dict.items():
        df.to_excel(writer, sheet_name=sheet_name, index=False)

print("合并完成！")