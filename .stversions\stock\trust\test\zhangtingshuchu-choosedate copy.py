import akshare as ak
import pandas as pd
import pywencai
from datetime import datetime, date, timedelta

def get_trade_dates():
    """获取交易日历数据"""
    try:
        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates_df['trade_date'] = pd.to_datetime(trade_dates_df['trade_date'])
        return trade_dates_df
    except Exception as e:
        print(f"获取数据时出错: {str(e)}")
        return pd.DataFrame()

def get_market_data(date, query_type):
    """获取市场数据"""
    query_map = {
        'limit_up': f"非ST,沪深主板，{date.strftime('%Y%m%d')}涨停",
        'limit_down': f"非ST,沪深主板，{date.strftime('%Y%m%d')}跌停",
        'poban': f"非ST,沪深主板，{date.strftime('%Y%m%d')}曾涨停",
        'yesterday_limit_up': f"非ST,沪深主板，{date.strftime('%Y%m%d')}涨跌幅"
    }

    try:
        df = pywencai.get(
            query=query_map[query_type],
            sort_key='成交金额',
            sort_order='desc',
            loop=True
        )
        return df if df is not None and not df.empty else None
    except Exception as e:
        print(f"获取{query_type}数据时出错: {str(e)}")
        return None

def get_concept_counts(df, date, min_count=2):
    """统计涨停概念"""
    if df is None or df.empty:
        return pd.DataFrame()

    reason_col = f'涨停原因类别[{date.strftime("%Y%m%d")}]'
    if reason_col not in df.columns:
        possible_cols = [col for col in df.columns if '涨停原因' in col or '概念' in col]
        if possible_cols:
            reason_col = possible_cols[0]
        else:
            return pd.DataFrame()

    try:
        concepts_series = df[reason_col].dropna().astype(str)
        concepts_series = concepts_series[~concepts_series.isin(['nan', 'None', '', '无'])]

        if concepts_series.empty:
            return pd.DataFrame()

        concepts = concepts_series.str.split('+').explode().reset_index(drop=True)
        concepts = concepts.str.strip()
        concepts = concepts[concepts != '']

        if concepts.empty:
            return pd.DataFrame()

        concept_counts = concepts.value_counts()
        concept_counts = concept_counts[concept_counts >= min_count]

        if concept_counts.empty:
            return pd.DataFrame()

        concept_stats = [
            {'指标': '热门概念', '数值': f"{concept}({count})"}
            for concept, count in concept_counts.head(10).items()
        ]

        return pd.DataFrame(concept_stats)

    except Exception as e:
        print(f"统计概念时出错: {str(e)}")
        return pd.DataFrame()

def get_yesterday_limit_up_performance(selected_date, previous_df):
    """计算昨日涨停股票今日表现"""
    if previous_df is None or previous_df.empty:
        return 0, 0, 0.0

    try:
        yesterday_codes = set(previous_df['股票代码'].tolist())
        today_market_df = get_market_data(selected_date, 'yesterday_limit_up')

        if today_market_df is None or today_market_df.empty:
            return 0, 0, 0.0

        yesterday_zt_today_df = today_market_df[
            today_market_df['股票代码'].isin(yesterday_codes)
        ].copy()

        if yesterday_zt_today_df.empty:
            return 0, 0, 0.0

        zhangdiefu_col = None
        for col in yesterday_zt_today_df.columns:
            if '涨跌幅' in col or '涨幅' in col:
                zhangdiefu_col = col
                break

        if zhangdiefu_col is None:
            return 0, 0, 0.0

        yesterday_zt_today_df[zhangdiefu_col] = pd.to_numeric(
            yesterday_zt_today_df[zhangdiefu_col], errors='coerce'
        )

        valid_data = yesterday_zt_today_df.dropna(subset=[zhangdiefu_col])

        if valid_data.empty:
            return 0, 0, 0.0

        up_count = len(valid_data[valid_data[zhangdiefu_col] > 0])
        total_count = len(valid_data)
        up_rate = round(up_count / total_count * 100, 2) if total_count > 0 else 0

        return up_count, total_count, up_rate

    except Exception as e:
        print(f"计算昨日涨停今日表现时出错: {str(e)}")
        return 0, 0, 0.0

def calculate_market_stats(selected_date, selected_df, previous_df, poban_df, selected_limit_down_df):
    """计算市场统计数据"""
    date_str = selected_date.strftime("%Y%m%d")
    stats = []

    # 基础数据统计
    total_limit_up = len(selected_df) if selected_df is not None and not selected_df.empty else 0
    total_limit_down = len(selected_limit_down_df) if selected_limit_down_df is not None and not selected_limit_down_df.empty else 0
    total_poban = len(poban_df) if poban_df is not None and not poban_df.empty else 0

    # 计算首板数
    first_board_count = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            selected_df_copy = selected_df.copy()
            selected_df_copy[days_col] = pd.to_numeric(selected_df_copy[days_col], errors='coerce')
            first_board_count = len(selected_df_copy[selected_df_copy[days_col] == 1])

    # 计算最高板
    max_board = 0
    if selected_df is not None and not selected_df.empty:
        days_col = f'连续涨停天数[{date_str}]'
        if days_col in selected_df.columns:
            selected_df_copy = selected_df.copy()
            selected_df_copy[days_col] = pd.to_numeric(selected_df_copy[days_col], errors='coerce')
            max_board = selected_df_copy[days_col].max()
            if pd.isna(max_board):
                max_board = 0

    # 计算破板率（破板家数/（涨停家数+破板家数））
    total_attempt = total_limit_up + total_poban  # 总尝试涨停家数
    poban_rate = round(total_poban / total_attempt * 100, 2) if total_attempt > 0 else 0

    # 计算昨日涨停今日上涨率
    up_count, total_count, yesterday_up_rate = get_yesterday_limit_up_performance(selected_date, previous_df)

    stats.extend([
        {'指标': '日期', '数值': selected_date.strftime('%Y-%m-%d')},
        {'指标': '涨停家数', '数值': total_limit_up},
        {'指标': '跌停家数', '数值': total_limit_down},
        {'指标': '首板家数', '数值': first_board_count},
        {'指标': '最高板数', '数值': int(max_board) if max_board > 0 else 0},
        {'指标': '破板家数', '数值': total_poban},
        {'指标': '破板率', '数值': f"{poban_rate}%"},
        {'指标': '昨日涨停今日上涨', '数值': f"{up_count}/{total_count}"},
        {'指标': '昨日涨停今日上涨率', '数值': f"{yesterday_up_rate}%"}
    ])

    return pd.DataFrame(stats)

def calculate_promotion_rates_detailed(current_df, previous_df, current_date, previous_date):
    """计算详细的连板晋级率"""
    if current_df is None or current_df.empty or previous_df is None or previous_df.empty:
        return pd.DataFrame()

    current_days_col = f'连续涨停天数[{current_date.strftime("%Y%m%d")}]'
    previous_days_col = f'连续涨停天数[{previous_date.strftime("%Y%m%d")}]'

    stats = []

    # 处理数据
    current_df_copy = current_df.copy()
    if current_days_col in current_df_copy.columns:
        current_df_copy[current_days_col] = pd.to_numeric(current_df_copy[current_days_col], errors='coerce')
        current_df_copy[current_days_col] = current_df_copy[current_days_col].fillna(1)
    else:
        current_df_copy[current_days_col] = 1

    previous_df_copy = previous_df.copy()
    if previous_days_col in previous_df_copy.columns:
        previous_df_copy[previous_days_col] = pd.to_numeric(previous_df_copy[previous_days_col], errors='coerce')
        previous_df_copy[previous_days_col] = previous_df_copy[previous_days_col].fillna(1)
    else:
        previous_df_copy[previous_days_col] = 1

    # 统计各板块数量
    for i in range(1, 7):
        count = len(current_df_copy[current_df_copy[current_days_col] == i])
        stats.append({'指标': f'{i}板数量', '数值': count})

    count_6plus = len(current_df_copy[current_df_copy[current_days_col] >= 7])
    stats.append({'指标': '6板以上数量', '数值': count_6plus})

    # 计算晋级率
    total_prev = len(previous_df_copy)
    total_curr_promoted = len(current_df_copy[current_df_copy[current_days_col] > 1])
    total_rate = round(total_curr_promoted / total_prev * 100, 2) if total_prev > 0 else 0
    stats.append({'指标': '总晋级率', '数值': f"{total_rate}%"})

    for i in range(1, 7):
        prev_count = len(previous_df_copy[previous_df_copy[previous_days_col] == i])
        curr_count = len(current_df_copy[current_df_copy[current_days_col] == i + 1])
        rate = round(curr_count / prev_count * 100, 2) if prev_count > 0 else 0
        stats.append({'指标': f'{i}进{i+1}晋级率', '数值': f"{rate}% ({curr_count}/{prev_count})"})

    prev_count_6plus = len(previous_df_copy[previous_df_copy[previous_days_col] >= 7])
    curr_count_7plus = len(current_df_copy[current_df_copy[current_days_col] >= 8])
    rate_6plus = round(curr_count_7plus / prev_count_6plus * 100, 2) if prev_count_6plus > 0 else 0
    stats.append({'指标': '6板以上晋级率', '数值': f"{rate_6plus}% ({curr_count_7plus}/{prev_count_6plus})"})

    return pd.DataFrame(stats)

def export_market_analysis(selected_date, output_file):
    """导出市场分析数据到Excel"""
    trade_dates_df = get_trade_dates()
    if trade_dates_df.empty:
        print("无法获取交易日数据")
        return

    trading_dates = trade_dates_df['trade_date'].dt.date.tolist()
    previous_dates = [d for d in trading_dates if d < selected_date]
    if not previous_dates:
        print("没有找到前一交易日数据")
        return

    previous_date = max(previous_dates)
    print(f"分析日期: {selected_date.strftime('%Y-%m-%d')} (对比前一交易日: {previous_date.strftime('%Y-%m-%d')})")

    # 获取数据
    selected_df = get_market_data(selected_date, 'limit_up')
    previous_df = get_market_data(previous_date, 'limit_up')
    poban_df = get_market_data(selected_date, 'poban')
    selected_limit_down_df = get_market_data(selected_date, 'limit_down')

    # 计算统计数据
    market_stats = calculate_market_stats(
        selected_date, selected_df, previous_df, poban_df, selected_limit_down_df
    )

    promotion_rates = calculate_promotion_rates_detailed(selected_df, previous_df, selected_date, previous_date)
    concept_counts = get_concept_counts(selected_df, selected_date, min_count=2)

    # 导出到Excel
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            all_stats = pd.concat([
                market_stats,
                promotion_rates,
                concept_counts
            ], ignore_index=True)

            if not all_stats.empty:
                all_stats.to_excel(writer, sheet_name='市场统计汇总', index=False)

        print(f"数据已成功导出到 {output_file}")

    except Exception as e:
        print(f"导出Excel文件时出错: {str(e)}")
        return

    # 显示关键统计信息
    print("关键指标:")
    for _, row in market_stats.iterrows():
        print(f"   {row['指标']}: {row['数值']}")

def main():
    trade_dates_df = get_trade_dates()

    if not trade_dates_df.empty:
        latest_trade_date = trade_dates_df['trade_date'].dt.date.max()

        print(f"可选日期范围: 至 {latest_trade_date}")
        print("1. 输入具体日期（格式：YYYY-MM-DD）")
        print("2. 输入相对日期（如：-1 表示最新交易日的前一天）")
        print("3. 直接回车使用最新交易日")

        while True:
            date_input = input("请输入日期或相对天数: ").strip()

            try:
                if not date_input:
                    selected_date = latest_trade_date
                    break
                elif date_input.startswith('-'):
                    days_back = int(date_input)
                    all_dates = sorted(trade_dates_df['trade_date'].dt.date.unique())
                    latest_idx = all_dates.index(latest_trade_date)
                    target_idx = latest_idx + days_back
                    if 0 <= target_idx < len(all_dates):
                        selected_date = all_dates[target_idx]
                        break
                    else:
                        print("超出可选范围，请重新输入")
                else:
                    input_date = datetime.strptime(date_input, '%Y-%m-%d').date()
                    all_dates = trade_dates_df['trade_date'].dt.date
                    if input_date in all_dates.values:
                        selected_date = input_date
                    else:
                        selected_date = all_dates[all_dates <= input_date].max()
                    break
            except ValueError:
                print("输入格式错误，请重新输入")

        print(f"已选择日期: {selected_date}")
        output_file = f"market_analysis_{selected_date.strftime('%Y%m%d')}.xlsx"
        export_market_analysis(selected_date, output_file)
    else:
        print("获取交易日历数据失败")

if __name__ == "__main__":
    main() 