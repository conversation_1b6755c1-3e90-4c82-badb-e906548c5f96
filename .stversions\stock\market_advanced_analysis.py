import streamlit as st
import pandas as pd
import pywencai
from datetime import datetime, timedelta
import akshare as ak

# 设置页面
st.set_page_config(
    page_title="市场高级分析",
    page_icon="📈",
    layout="wide"
)

# 设置中文显示
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)

@st.cache_data(ttl=3600)
def get_trading_calendar():
    """获取交易日历"""
    try:
        calendar = ak.tool_trade_date_hist_sina()
        # 转换为datetime.date对象
        calendar['trade_date'] = pd.to_datetime(calendar['trade_date']).dt.date
        return sorted(calendar['trade_date'].tolist())
    except Exception as e:
        st.error(f"获取交易日历失败: {str(e)}")
        return []

@st.cache_data(ttl=3600)
def get_continuous_limit_up(date_str):
    """获取连板数据"""
    try:
        query = f"非ST，{date_str}连续涨停天数，涨停原因，涨停封单额"
        df = pd.DataFrame(pywencai.get(query=query, loop=True))
        
        # 调试信息
        st.write("连板数据查询结果：")
        st.write(df)
        st.write("列名：", df.columns.tolist())
        
        if len(df) == 0:
            return None
        return df
    except Exception as e:
        st.error(f"获取连板数据失败: {str(e)}")
        st.write("错误详情：", e)
        return None

@st.cache_data(ttl=3600)
def get_concept_stats(date_str):
    """获取概念板块统计"""
    try:
        query = f"非ST，{date_str}涨停，概念板块，所属概念，涨跌幅"
        df = pd.DataFrame(pywencai.get(query=query, loop=True))
        
        # 调试信息
        st.write("概念数据查询结果：")
        st.write(df)
        st.write("列名：", df.columns.tolist())
        
        if len(df) == 0:
            return None
        return df
    except Exception as e:
        st.error(f"获取概念统计失败: {str(e)}")
        st.write("错误详情：", e)
        return None

def calculate_advance_rates(df, date_str):
    """计算晋级率"""
    if df is None or len(df) == 0:
        return 0, 0, 0
    
    # 尝试不同的列名
    days_columns = [
        f'连续涨停天数[{date_str}]',
        '连续涨停天数',
        '连板天数',
        '连板数'
    ]
    
    # 查找实际的列名
    days_col = next((col for col in days_columns if col in df.columns), None)
    
    if not days_col:
        st.warning(f"未找到连板天数列，可用列名: {df.columns.tolist()}")
        return 0, 0, 0
    
    df[days_col] = pd.to_numeric(df[days_col], errors='coerce')
    
    # 计算各连板数量
    one_board = len(df[df[days_col] == 1])
    two_board = len(df[df[days_col] == 2])
    three_board = len(df[df[days_col] == 3])
    four_board = len(df[df[days_col] == 4])
    
    # 调试信息
    st.write("连板数量统计：")
    st.write({
        "一板": one_board,
        "二板": two_board,
        "三板": three_board,
        "四板": four_board
    })
    
    # 计算晋级率
    rate_1to2 = round(two_board / one_board * 100, 2) if one_board > 0 else 0
    rate_2to3 = round(three_board / two_board * 100, 2) if two_board > 0 else 0
    rate_3to4 = round(four_board / three_board * 100, 2) if three_board > 0 else 0
    
    return rate_1to2, rate_2to3, rate_3to4

def analyze_concepts(df, date_str):
    """分析概念板块数据"""
    if df is None or len(df) == 0:
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    # 尝试不同的列名
    concept_columns = ['所属概念', '概念板块', '概念']
    change_columns = ['涨跌幅', '涨幅', '涨跌幅度']
    limit_up_columns = [f'涨停类型[{date_str}]', '涨停类型', '涨停']
    limit_down_columns = [f'跌停类型[{date_str}]', '跌停类型', '跌停']
    
    # 查找实际的列名
    concept_col = next((col for col in concept_columns if col in df.columns), None)
    change_col = next((col for col in change_columns if col in df.columns), None)
    limit_up_col = next((col for col in limit_up_columns if col in df.columns), None)
    limit_down_col = next((col for col in limit_down_columns if col in df.columns), None)
    
    if not all([concept_col, change_col]):
        st.warning(f"缺少必要的列，可用列名: {df.columns.tolist()}")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    # 展开概念
    df_exploded = df.assign(概念=df[concept_col].str.split(';')).explode('概念')
    
    # 计算涨幅最高的概念
    top_gain = df_exploded.groupby('概念')[change_col].mean().sort_values(ascending=False).head(5)
    
    # 计算跌幅最大的概念
    top_loss = df_exploded.groupby('概念')[change_col].mean().sort_values().head(5)
    
    # 计算涨停最多的概念
    if limit_up_col:
        top_limit_up = df_exploded[df_exploded[limit_up_col].notna()].groupby('概念').size().sort_values(ascending=False).head(5)
    else:
        top_limit_up = pd.Series()
    
    # 计算跌停最多的概念
    if limit_down_col:
        top_limit_down = df_exploded[df_exploded[limit_down_col].notna()].groupby('概念').size().sort_values(ascending=False).head(5)
    else:
        top_limit_down = pd.Series()
    
    return top_gain, top_loss, top_limit_up, top_limit_down

def main():
    st.title("📊 市场高级分析")
    
    # 日期选择
    trading_days = get_trading_calendar()
    if not trading_days:
        st.error("无法获取交易日历")
        return
    
    # 获取最新和最早的交易日期
    max_date = trading_days[-1]  # 使用排序后的最后一个日期
    min_date = max_date - timedelta(days=365)
    
    # 获取用户选择的日期
    col1, col2 = st.columns([3, 1])
    with col1:
        user_date = st.date_input(
            "选择日期",
            value=max_date,
            min_value=min_date,
            max_value=max_date
        )
    with col2:
        is_trading_day = user_date in set(trading_days)
        st.metric(
            "是否为交易日",
            "是" if is_trading_day else "否",
            delta=None,
            delta_color="normal"
        )
    
    # 找到最近的交易日
    trading_days_set = set(trading_days)  # 转换为集合以提高查找效率
    
    if user_date in trading_days_set:
        selected_date = user_date
    else:
        # 如果选择的不是交易日，找到最近的交易日
        while user_date not in trading_days_set and user_date >= min_date:
            user_date -= timedelta(days=1)
        
        if user_date >= min_date:
            selected_date = user_date
            st.info(f"已自动调整到最近的交易日: {selected_date.strftime('%Y-%m-%d')}")
        else:
            selected_date = max_date
            st.warning("未找到最近的交易日，已调整到最新交易日")
    
    # 获取数据
    with st.spinner("正在获取数据..."):
        # 获取当日市场数据
        current_date = selected_date.strftime('%Y%m%d')
        continuous_limit_up = get_continuous_limit_up(current_date)
        concept_data = get_concept_stats(current_date)
        
        # 计算晋级率
        rate_1to2, rate_2to3, rate_3to4 = calculate_advance_rates(continuous_limit_up, current_date)
        
        # 分析概念数据
        top_gain, top_loss, top_limit_up, top_limit_down = analyze_concepts(concept_data, current_date)
    
    # 连板统计
    st.subheader("连板数据")
    if continuous_limit_up is not None:
        # 显示连板数量统计
        days_col = f'连续涨停天数[{current_date}]'
        if days_col in continuous_limit_up.columns:
            continuous_limit_up[days_col] = pd.to_numeric(continuous_limit_up[days_col], errors='coerce')
            board_counts = continuous_limit_up[days_col].value_counts().sort_index()
            
            st.write("连板数量统计：")
            board_stats = pd.DataFrame({
                '连板数': board_counts.index,
                '个股数量': board_counts.values
            })
            st.dataframe(board_stats, hide_index=True)
        
        # 显示晋级率
        st.write("晋级率统计：")
        cols = st.columns(3)
        with cols[0]:
            st.metric("一进二晋级率", f"{rate_1to2}%")
        with cols[1]:
            st.metric("二进三晋级率", f"{rate_2to3}%")
        with cols[2]:
            st.metric("三进四晋级率", f"{rate_3to4}%")
    
    # 概念板块分析
    st.subheader("概念板块分析")
    if len(top_gain) > 0 or len(top_loss) > 0:
        col1, col2 = st.columns(2)
        with col1:
            st.write("涨幅最高的概念")
            gain_df = top_gain.reset_index()
            gain_df.columns = ['板块名称', '平均涨幅']
            st.dataframe(gain_df, hide_index=True)
        with col2:
            st.write("跌幅最大的概念")
            loss_df = top_loss.reset_index()
            loss_df.columns = ['板块名称', '平均跌幅']
            st.dataframe(loss_df, hide_index=True)
    
    if len(top_limit_up) > 0 or len(top_limit_down) > 0:
        col1, col2 = st.columns(2)
        with col1:
            st.write("涨停最多的概念")
            limit_up_df = top_limit_up.reset_index()
            limit_up_df.columns = ['板块名称', '涨停家数']
            st.dataframe(limit_up_df, hide_index=True)
        with col2:
            st.write("跌停最多的概念")
            limit_down_df = top_limit_down.reset_index()
            limit_down_df.columns = ['板块名称', '跌停家数']
            st.dataframe(limit_down_df, hide_index=True)

if __name__ == "__main__":
    main()