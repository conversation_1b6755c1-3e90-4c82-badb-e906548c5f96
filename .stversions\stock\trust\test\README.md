# 涨停分析脚本

## 功能说明

这是一个精简的涨停分析脚本，主要功能包括：

1. **基础市场数据统计**
   - 涨停家数、跌停家数、首板家数、最高板数
   - 破板家数、破板率
   - 昨日涨停今日上涨数量和上涨率

2. **板块分布统计**
   - 1板到6板的具体数量
   - 6板以上数量（7板及以上合并）

3. **晋级率计算**
   - 总晋级率
   - 1进2、2进3、3进4、4进5、5进6、6进7晋级率
   - 6板以上晋级率
   - **所有晋级率都会显示，即使为0%**

4. **热门概念统计**
   - 显示出现次数≥2的概念
   - 最多显示前10个热门概念

## 使用方法

```bash
python zhangtingshuchu.py
```

### 日期选择方式

1. **直接回车**: 使用最新交易日
2. **输入具体日期**: 如 `2024-12-02`
3. **输入相对日期**: 如 `-1` 表示最新交易日的前一天

## 输出文件

- 生成Excel文件: `market_analysis_YYYYMMDD.xlsx`
- 包含一个工作表: `市场统计汇总`
- 所有数据按类别组织在一个表格中

## 输出示例

```
关键指标:
   日期: 2024-12-02
   涨停家数: 45
   跌停家数: 3
   首板家数: 25
   最高板数: 8
   破板家数: 12
   破板率: 21.05%
   昨日涨停今日上涨: 28/50
   昨日涨停今日上涨率: 56.0%
```

Excel文件中还包含：
- 1板数量: 25
- 2板数量: 8
- 3板数量: 5
- 4板数量: 3
- 5板数量: 2
- 6板数量: 1
- 6板以上数量: 1
- 总晋级率: 40.0%
- 1进2晋级率: 32.0% (8/25)
- 2进3晋级率: 62.5% (5/8)
- 3进4晋级率: 60.0% (3/5)
- 4进5晋级率: 66.7% (2/3)
- 5进6晋级率: 50.0% (1/2)
- 6进7晋级率: 0.0% (0/1)
- 6板以上晋级率: 100.0% (1/1)
- 热门概念: 人工智能(8)
- 热门概念: 新能源(6)

## 依赖库

```bash
pip install akshare pywencai pandas openpyxl
```

## 主要优化

- ✅ 修复昨日涨停今日上涨率计算错误
- ✅ 按要求显示1-6板及6板以上数量
- ✅ 显示所有晋级率（包括0%）
- ✅ 移除不必要的股票详情输出
- ✅ 精简代码，提高可读性
- ✅ 保留核心功能，删除冗余代码

## 注意事项

1. 确保网络连接稳定
2. 数据获取可能需要一些时间
3. 如果数据获取失败，检查网络或稍后重试
